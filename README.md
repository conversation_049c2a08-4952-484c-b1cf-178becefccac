项目名称：阿拉钉统一登录（sso）、公共主页
---

主要工具介绍
---
工具名                   | 作用
----------------------- | ------
vue v2.5                | data binding
vue-router v3           | vue路由
webpack v4              | 本地发开、打包发布
webpack-dev-server v3   | 本地启mock服务
webpack-bundle-analyzer | 打包后文件分析
axios                   | 处理get/post
mockjs                  | 制造模拟数据

功能介绍
---
### 开发
2. sourcemap，很好定位。
3. eslint，stylelint强制部分代码规范。
4. 热替换。
6. 使用mockjs模拟接口返回数据。
7. scss支持识别2倍3倍图加载（除了接口获取的图，所有图都用背景图方案）
8. git hooks，防止不符合规范的提交

### 发布
1. 将第三方js库打包成vendor.js，从而使项目业务代码修改也不会影响基本稳定的三方js代码，充分利用浏览器缓存。
2. 将css代码分为两份，一份为基础代码，即上线后几乎不变，另一份为迭代代码，从而充分利用浏览器缓存。
3. 发布以后的所有文件会加md5后缀，从而充分利用浏览器缓存。
4. tree shaking。
5. dynamic import，代码不会打包到一个js文件里，而会分成n个按需加载js。

常用命令
---
### 开发
#### 1.模拟数据
`npm run dev:mock`

#### 2.页面开发
`npm run dev`

### 代码大小分析
`npm run analyze`

### 发布
`npm run build`
