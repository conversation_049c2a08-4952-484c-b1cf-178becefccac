const fs = require('fs')
const path = require('path')

const tips = '// This file is auto gererated by build/bin/build-entry.js'
const dir = path.join(__dirname, '../../mock/')
const cnMocks = fs.readdirSync(path.join(dir));
console.info('--------mock list:-----',cnMocks)
const mocks = [...cnMocks]
  .filter(name => name !== 'index.js')
  .map(name => {
    name = name.replace(/\.js$/, '')
    return `import './${name}'`
  })

const content = `${tips}

${mocks.join('\n')}
`
fs.writeFileSync(path.join(__dirname, '../../mock/index.js'), content)
