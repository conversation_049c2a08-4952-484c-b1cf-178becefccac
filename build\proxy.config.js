//*************
//注意 由于此文件每个人都会因调试环境不同而做更改 因此不要提交这个文件的改动
//在项目目录执行 git update-index --skip-worktree build/proxy.config.js 可以忽略次文件的改动
//反之 执行 git update-index --no-skip-worktree build/proxy.config.js 可以恢复追踪此文件的改动
//*************

const os = require("os")

// 获取本机ip·
function getIP() {
  const interfaces = os.networkInterfaces()
  let addresses = []
  for (let k in interfaces) {
    for (let k2 in interfaces[k]) {
      let address = interfaces[k][k2]
      if (address.family === "IPv4" && !address.internal) {
        addresses.push(address.address)
      }
    }
  }
  return addresses[0]
}

const apiUrl = {
  dev: "https://webapi-dev.lanmaoly.com",
  qa: "http://*************:8051",
  qa2: "https://webapi-qa.lanmaoly.com",
  prod: "https://contract.olading.com",
  oladingUser: "http://*************:7300/mock/5dedac3855fc820022af65e2/olading-user",
  merchant: "http://*************:7300/mock/5dedb43955fc820022af65fd/merchant",
  //和你联调人的地址 将下面 apiUrl.dev 改为 apiUrl.custom即可
  custom: "https://pre-stage-yq.olading.com/"
}
//不启用mock
let proxyTable = [
  {
    context: ["/api"],
    target: apiUrl.qa2,
    secure: false,
    changeOrigin: true
  }
]

//如果当前启用mock模式
if (process.env.MOCK) {
  //这里两个条件是互斥的
  let onlyMock = ["/api/merchant/notice/list", "/api/merchant/todo-something/list"] //仅mock这些
  let notMock = ["/api/**/create", "/api/**/show"] //仅不mock这些

  //在此动态控制 不需要哪个就清空
  notMock = []
  // onlyMock = [];

  if (onlyMock.length > 0 && notMock.length > 0) {
    throw "onlyMock 和 notMock 不能同时存在元素"
  }

  proxyTable = [
    {
      context: onlyMock.length > 0 ? ["/api"] : notMock,
      target: apiUrl.dev,
      secure: false,
      changeOrigin: true
    },
    // {
    //   context: onlyMock.length > 0 ? onlyMock : ['/api/olading-user'],
    //   target: apiUrl.oladingUser,
    //   secure: false,
    //   changeOrigin: true,
    //   pathRewrite: {'/olading-user': ''}
    // },
    {
      context: onlyMock.length > 0 ? onlyMock : ["/api/merchant/"],
      target: apiUrl.merchant,
      secure: false,
      changeOrigin: true,
      pathRewrite: { "/merchant": "" }
    }
  ]

  if (onlyMock.length > 0) {
    proxyTable.push(proxyTable.shift())
  }
}

const ip = getIP(0) || "localhost"

// console.info('=========proxyTable========',proxyTable)

module.exports = {
  ip: ip,
  proxyTable
  // domain: `http://${ip}`,
  // port: '7777'
}
