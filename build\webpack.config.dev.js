// const getOldMockProxy = require("old-mock-proxy")
const webpack = require('webpack')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const FriendlyErrorsPlugin = require('friendly-errors-webpack-plugin')
const webpackConfigBase = require('./webpack.config.base.js')
const proxyConfig = require('./proxy.config.js')
const CopyWebpackPlugin = require('copy-webpack-plugin')
const MOCK = process.env.MOCK
const path = require('path')
// const options = {
//   mode: {
//     type: "default",
//     // modeList:['/api/**/bind_info']
//   },
//   mockTargetList: [
//     {
//       context: ["/api"],
//       url: "https://webapi-qa.lanmaoly.com"
//     },
//     // {
//     //   context: ["/api/olading-user"],
//     //   url: "http://172.19.60.201:7300/mock/5dedac3855fc820022af65e2/olading-user"
//     // },
//     // {
//     //   context: ["/api/merchant"],
//     //   url: "http://172.19.60.201:7300/mock/5dedb43955fc820022af65fd/merchant"
//     // }
//   ]
// }
// const proxyConfig = getOldMockProxy(options)
// if(MOCK){
//   const shell = require('shelljs')
//
//   console.log('Starting', 'build-entry')
//   shell.exec(`node './build/bin/build-entry.js'`)
//   console.log('Finished', 'build-entry', 'green')
//
// }

const config = Object.assign(webpackConfigBase.config, {
  mode: 'development',
  devtool: 'eval-source-map',
  // 入口
  entry: {
    app: ['babel-polyfill', webpackConfigBase.resolve('src/index.js')]
  },
  // 输出
  output: {
    path: webpackConfigBase.resolve('dev'),
    filename: '[name].js',
    publicPath: '/'
  },
  plugins: [
    // html 模板插件
    new HtmlWebpackPlugin({
      filename: 'index.html',
      template: webpackConfigBase.resolve('src/index.ejs')
    }),
    new CopyWebpackPlugin([
      // 复制favicon到dist
      {
        from: webpackConfigBase.static,
        to: webpackConfigBase.resolve('dev/static')
      },
      {
        from: path.resolve(__dirname,'../config'),
        to: 'config',
        ignore:['.*']
      }
    ]),
    // 热替换插件
    new webpack.HotModuleReplacementPlugin(),
    // 更友好地输出错误信息
    new FriendlyErrorsPlugin(),
    // make sure to include the plugin for the magic
    webpackConfigBase.VueLoaderPluginInstance,
    // 定义全局常量
    new webpack.DefinePlugin({
      'process.env': {
        MOCK: JSON.stringify(MOCK)
      }
    })
  ],
  devServer: {
    useLocalIp:true,
    proxy: proxyConfig.proxyTable,
    // host: proxyConfig.ip,
    host: '0.0.0.0',
    disableHostCheck: true, // 为了手机可以访问
    publicPath: '/',
    contentBase: webpackConfigBase.resolve('dev'), // 本地服务器所加载的页面所在的目录
    historyApiFallback: true, // 为了SPA应用服务
    quiet: true, //安静模式 省略多余console
    inline: true, //实时刷新
    hot: true, // 使用热加载插件 HotModuleReplacementPlugin
    clientLogLevel: "none"  // HRM WDS 在浏览器控制台的输出
  }
})

module.exports = config
