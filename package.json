{"name": "vue-lan<PERSON><PERSON>", "version": "1.0.0", "description": "vance_vue_webpack", "main": "index.js", "scripts": {"dev": "webpack-dev-server --progress --colors --open --config build/webpack.config.dev.js", "dev:mock": "cross-env MOCK=active webpack-dev-server --progress --colors --open --config build/webpack.config.dev.js", "analyze": "cross-env NODE_ENV=production ANALYZE=active build -p --progress --colors --config build/webpack.config.prod.js", "build": "cross-env NODE_ENV=production webpack -p --progress --colors --config build/webpack.config.prod.js", "lint": "npm run lint:js && npm run lint:css", "lint:js": "eslint --ext .vue,.js src", "lint:css": "stylelint 'src/**/*.(vue|scss)' --syntax scss", "format": "prettier --config .prettierrc --write \"src/**/*.{js,scss,vue}\"", "format:watch": "onchange 'src/**/*.js' -- prettier --write {{changed}}"}, "husky": {"hooks": {"pre-commit": "npm run format"}}, "author": "lan<PERSON><PERSON>", "license": "MIT", "devDependencies": {"autoprefixer": "^7.2.6", "babel": "^6.23.0", "babel-core": "^6.26.3", "babel-eslint": "^8.2.6", "babel-loader": "^7.1.5", "babel-plugin-istanbul": "^5.1.0", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "clean-webpack-plugin": "^0.1.19", "concurrently": "^3.6.1", "copy-webpack-plugin": "^4.5.3", "cross-env": "^5.2.0", "css-loader": "^3.4.2", "eslint": "^5.7.0", "eslint-friendly-formatter": "^4.0.1", "eslint-loader": "^2.1.1", "eslint-plugin-html": "^4.0.6", "eslint-plugin-vue": "^5.0.0-beta.3", "file-loader": "^2.0.0", "friendly-errors-webpack-plugin": "^1.7.0", "html-webpack-plugin": "^3.2.0", "husky": "^1.1.2", "inline-manifest-webpack-plugin": "^4.0.2", "koa": "^2.5.3", "koa-bodyparser": "^4.2.1", "koa-router": "^7.4.0", "koa-send": "^4.1.3", "marked": "^0.4.0", "mini-css-extract-plugin": "^0.4.4", "mockjs": "^1.1.0", "node-sass": "^4.14.1", "nodemon": "^1.18.9", "onchange": "^6.1.0", "opn": "^5.4.0", "postcss": "^6.0.23", "postcss-loader": "^2.1.6", "prettier": "1.19.1", "sass-loader": "^8.0.2", "shelljs": "^0.8.3", "style-loader": "^0.19.1", "stylelint": "^9.6.0", "stylelint-config-standard": "^18.2.0", "stylelint-processor-html": "^1.0.0", "uglifyjs-webpack-plugin": "^2.0.1", "url-loader": "^0.6.2", "vue-eslint-parser": "^3.2.2", "vue-loader": "^15.4.2", "vue-style-loader": "^4.1.2", "vue-template-compiler": "^2.5.17", "webpack": "^4.20.2", "webpack-bundle-analyzer": "^2.13.1", "webpack-cli": "^3.1.2", "webpack-dev-server": "^3.1.9", "webpack-inline-manifest-plugin": "^4.0.1"}, "dependencies": {"axios": "^0.18.0", "element-ui": "^2.10.0", "mint-ui": "^2.2.13", "olading-ui": "^0.0.8", "old-env-map": "^0.0.5", "old-fetch": "^0.0.16", "vant": "^2.1.2", "vue": "^2.5.17", "vue-draggable-resizable": "^2.0.0-rc1", "vue-router": "^3.0.1", "vuex": "^3.0.1", "vuex-persistedstate": "^2.5.4"}}