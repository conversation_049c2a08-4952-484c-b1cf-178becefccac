<template>
  <div style="height: 100%;">
    <router-view></router-view>
  </div>
</template>
<script>
import * as AT from "@/store/actionTypes"
export default {
  components: {},
  data() {
    return {}
  },
  computed: {},
  mounted() {
    // let urlInfo = this.getParam()
    // this.$store.commit(AT.URLINFO, urlInfo)
    // this.$store.commit(AT.LOGIN_TOKEN, urlInfo.token)
  },
  methods: {
    // 获取URL里面得信息
    // getParam() {
    //   let url = window.location.href
    //   if (url.indexOf("?") != -1) {
    //     var result = url.split("?")[1]
    //     var keyValue = result.split("&")
    //     var obj = {}
    //     for (var i = 0; i < keyValue.length; i++) {
    //       var item = keyValue[i].split("=")
    //       obj[item[0]] = item[1]
    //     }
    //     return obj
    //   }
    // }
  }
}
</script>
<style lang="scss">
#app,
body,
html {
  // height: 100%;
  background-color: rgb(245, 245, 245);
  height: 100%;
  width: 100%;
  scroll-behavior: smooth;
}
body {
  font-family: "HanHei SC", "PingFang SC", "Microsoft YaHei";
  font-size: 14px;
  direction: ltr;
  background-color: rgb(245, 245, 245);
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-rendering: optimizelegibility;
  position: relative;
}
* {
  margin: 0;
  padding: 0;
}
li {
  list-style: none;
}
a {
  text-decoration: none;
}
.no-xScroll {
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-scrollbar__view {
    height: 100%;
  }
}
.sign {
  .el-dialog__body {
    height: 240px;
  }
}
.el-dialog {
  color: #666;
  border-radius: 6px;

  .el-tabs__nav-wrap::after {
    background: #f7f7f7;
    height: 1px;
  }

  .el-dialog__title {
    line-height: 50px;
    font-size: 18px;
    color: #333;
  }

  .el-dialog__headerbtn {
    top: 16px;
  }

  .el-dialog__header {
    text-align: center;
    background: #f6f6f6;
    padding: 0;
    line-height: 45px;
    color: #333;
    border-radius: 6px;
    font-weight: bold;
  }

  .el-dialog__body {
    color: #666;
  }

  .el-form-item__content {
    .btn-content {
      text-align: center;
      overflow: hidden;

      .el-button {
        width: 45%;
      }

      .el-button:first-child {
        float: left;
      }

      .el-button:nth-child(2) {
        float: right;
      }
    }
  }

  .mytextArea {
    margin-bottom: 0;
  }

  .el-tabs__nav-scroll {
    .el-tabs__nav {
      width: 100%;

      .el-tabs__item {
        width: 50%;
        text-align: center;
        color: #999;
        height: 50px;
        line-height: 50px;
      }
    }

    .el-tabs__active-bar {
      width: 16px !important;
      position: absolute;
      left: 25%;
      background: #3d7ef5;
      border-radius: 2px;
      margin-left: -20px;
    }

    .el-tabs__item.is-active {
      color: #3d7ef5;
    }
  }
}
</style>
