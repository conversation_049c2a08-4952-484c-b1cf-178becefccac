@charset "utf-8";

$mainColor:#4687f5;

//彩色图标样式
.color-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

/*
系统共用样式
*/
body {
  background: #f7f8fd;
}

/* 强制文本换行 */
.textwrap,
.textwrap td,
.textwrap th {
  word-wrap: break-word;
  word-break: break-all;
}

.textwrap-table {
  table-layout: fixed;
}

textarea {
  resize: none !important;
}

/* Responsive images */
.a img {
  max-width: 100%;
}

.btn {
  border: none;
  padding: 0px 15px;
  line-height: 34px;
  height: 34px;
  outline: none;
  display: inline-block;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  border-radius: 4px;
  white-space: nowrap;
}

.btn:link,
.btn:visited,
.btn:hover,
.btn:active {
  color: #ffffff;
}

.btn[disabled="disabled"] {
  background: #d5d5d5 !important;
  opacity: 0.8;
  filter: alpha(opacity=80);
}

.btn:disabled {
  opacity: 0.8;
  filter: alpha(opacity=80);
}

input[disabled="disabled"],
textarea[disabled="disabled"] {
  background: #fff;
}

input:disabled,
textarea:disabled {
  background: #fff;
}

input[readonly="readonly"] {
  color: #777777;
}


input[type="text"],
input[type="password"],
input[type="tel"],
input[type="number"],
input[type="email"] {
  //height: 34px;
  font-size: 14px;
  font-weight: 400;
  color: #333;
  border-radius: 5px;
  outline: none;
  //vertical-align: middle;
  border: 1px solid #E6E8EC;
}

input::-webkit-contacts-auto-fill-button {
  visibility: hidden;
  display: none !important;
  pointer-events: none;
  position: absolute;
  right: 0;
  width: 0px;
  height: 0px;
}

::-moz-placeholder {
  color: #C6C6C6;
}

::-webkit-input-placeholder {
  color: #C6C6C6;
}

:-ms-input-placeholder {
  color: #C6C6C6;
}


input:-webkit-autofill {
  box-shadow: 0 0 0px 1000px white inset;
  -webkit-box-shadow: 0 0 0px 1000px white inset;
  outline: none;
}

textarea {
  resize: none;
  outline: none;
}

.hidden {
  display: none;
}

/*清除浮动*/
.clearfix:before,
.clearfix:after {
  content: "";
  display: table;
}

.clearfix:after {
  clear: both;
}

.clearfix {
  *zoom: 1;
}

/* ...隐藏 */
.ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
}

/*表单元素重定义*/
select {
  display: inline-block;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}

select[disabled="disabled"],
select[disabled="true"] {
  background-color: #f1f1f1;
  color: #999999;
}

select.fullWidth {
  width: 100%;
}

/*去除IE浏览器中所有搜索框的清空icon*/
.keyword-search input[type="text"]::-ms-clear {
  display: none;
}

// el-form-item required样式
.required {
  .el-form-item__label {
    &:before {
      content: '*';
      color: #ff4949;
      margin-right: 4px;
    }
  }
}

.display-flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

.flex1 {
  -webkit-flex: 1;
  /* Chrome */
  -moz-box-flex: 1;
  /* Firefox 19- */
  -ms-flex: 1;
  /* IE 10 */
  flex: 1;
  /* NEW,  Opera 12.1, Firefox 20+ */
}

//-----------自定义拖动控件样式----start---------------
.my-handle-class {
  position: absolute;
  background-color: pink;
  border: 1px solid black;
  border-radius: 50%;
  height: 10px;
  width: 10px;
  box-model: border-box;
  -webkit-transition: all 300ms linear;
  -ms-transition: all 300ms linear;
  transition: all 300ms linear;
}

.my-handle-class-tl {
  top: -10px;
  left: -10px;
  cursor: nw-resize;
}

.my-handle-class-tm {
  visibility: hidden;
  top: -10px;
  left: 50%;
  margin-left: -7px;
  cursor: n-resize;
}

.my-handle-class-tr {
  visibility: hidden;
  top: -10px;
  right: -10px;
  cursor: ne-resize;
}

.my-handle-class-ml {
  top: 50%;
  margin-top: -7px;
  left: -10px;
  cursor: w-resize;
}

.my-handle-class-mr {
  top: 50%;
  margin-top: -7px;
  right: -10px;
  cursor: e-resize;
}

.my-handle-class-bl {
  bottom: -10px;
  left: -10px;
  cursor: sw-resize;
}

.my-handle-class-bm {
  bottom: -10px;
  left: 50%;
  margin-left: -7px;
  cursor: s-resize;
}

.my-handle-class-br {
  bottom: -10px;
  right: -10px;
  cursor: se-resize;
}

.my-handle-class-tl:hover,
.my-handle-class-tr:hover,
.my-handle-class-tm:hover,
.my-handle-class-ml:hover,
.my-handle-class-mr:hover,
.my-handle-class-bl:hover,
.my-handle-class-bm:hover,
.my-handle-class-br:hover {
  background-color: $mainColor;
  transform: scale(1.3);
}

//-----------自定义拖动控件样式----end---------------