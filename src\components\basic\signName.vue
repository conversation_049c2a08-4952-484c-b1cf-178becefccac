<template>
  <div class="signatureBox">
    <div class="canvasBox" ref="canvasHW">
      <canvas
        @touchstart="touchStart"
        @touchmove="touchMove"
        @touchend="touchEnd"
        ref="canvasF"
        @mousedown="mouseDown"
        @mousemove="mouseMove"
        @mouseup="mouseUp"
        id="imgDiv"
      ></canvas>
      <div class="btnBox">
        <button @click="overwrite">清除</button>
        <button @click="saveImageInfo()">提交</button>
      </div>
    </div>
    <!-- <div align="center" class="login-btn">
      <el-button type="primary" class="skip" @click="skip" v-if="showSkip">跳过</el-button>
    </div>-->
  </div>
</template>
<script>
export default {
  name: "signature",
  props: {
    showSkip: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      points: [],
      canvasTxt: null,
      startX: 0,
      startY: 0,
      moveY: 0,
      moveX: 0,
      endY: 0,
      endX: 0,
      w: null,
      h: null,
      imgurl: "",
      isDown: false
    }
  },
  // created() {
  //   window.onresize = function() {
  //     var offsetWid =
  //       document.documentElement.clientWidth || document.body.clientWidth;
  //     var offsetHei =
  //       document.documentElement.clientHeight || document.body.clientHeight;
  //     var imgs = (document.getElementById("imgDiv").style.height =
  //       offsetHei + "px");
  //   };
  // },
  mounted() {
    let canvas = this.$refs.canvasF
    canvas.height = this.$refs.canvasHW.offsetHeight
    canvas.width = this.$refs.canvasHW.offsetWidth
    this.canvasTxt = canvas.getContext("2d")
  },
  components: {},
  methods: {
    backHome() {
      window.history.back()
    },
    dataURLtoFile() {},
    //提交
    saveImageInfo() {
      let mycanvas = document.getElementById("imgDiv")
      let imageSrc = mycanvas.toDataURL("image/png")
      let img = new Image()
      let file = null
      img.src = imageSrc
      img.onload = () => {
        file = this.dataURLtoBlob(imageSrc)
        if (this.points.length == 0) {
          this.$message.error("请设置签名")
        } else {
          // let imageSrcBase = imageSrc.split(",")[1]
          this.$emit("saveImageInfo", imageSrc)
        }
      }
    },
    skip() {
      this.$router.push("/home")
    },
    dataURLtoBlob(dataurl) {
      var arr = dataurl.split(","),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], {
        type: mime
      })
    },
    //电脑设备事件
    mouseDown(ev) {
      ev = ev || event
      ev.preventDefault()
      if (1) {
        let obj = {
          x: ev.offsetX,
          y: ev.offsetY
        }
        this.startX = obj.x
        this.startY = obj.y
        this.canvasTxt.beginPath()
        this.canvasTxt.moveTo(this.startX, this.startY)
        this.canvasTxt.lineTo(obj.x, obj.y)
        this.canvasTxt.stroke()
        this.canvasTxt.closePath()
        this.points.push(obj)
        this.isDown = true
      }
    },
    //移动设备事件
    touchStart(ev) {
      ev = ev || event
      ev.preventDefault()
      if (ev.touches.length == 1) {
        let obj = {
          x: ev.targetTouches[0].clientX,
          y: ev.targetTouches[0].clientY - 48
        }
        this.startX = obj.x
        this.startY = obj.y
        this.canvasTxt.beginPath()
        this.canvasTxt.moveTo(this.startX, this.startY)
        this.canvasTxt.lineTo(obj.x, obj.y)
        this.canvasTxt.stroke()
        this.canvasTxt.closePath()
        this.points.push(obj)
      }
    },
    //电脑设备事件
    mouseMove(ev) {
      ev = ev || event
      ev.preventDefault()
      if (this.isDown) {
        let obj = {
          x: ev.offsetX,
          y: ev.offsetY
        }
        this.moveY = obj.y
        this.moveX = obj.x
        this.canvasTxt.beginPath()
        this.canvasTxt.moveTo(this.startX, this.startY)
        this.canvasTxt.lineTo(obj.x, obj.y)
        this.canvasTxt.stroke()
        this.canvasTxt.closePath()
        this.startY = obj.y
        this.startX = obj.x
        this.points.push(obj)
      }
    },
    //移动设备事件
    touchMove(ev) {
      ev = ev || event
      ev.preventDefault()
      if (ev.touches.length == 1) {
        let obj = {
          x: ev.targetTouches[0].clientX,
          y: ev.targetTouches[0].clientY - 48
        }
        this.moveY = obj.y
        this.moveX = obj.x
        this.canvasTxt.beginPath()
        this.canvasTxt.moveTo(this.startX, this.startY)
        this.canvasTxt.lineTo(obj.x, obj.y)
        this.canvasTxt.stroke()
        this.canvasTxt.closePath()
        this.startY = obj.y
        this.startX = obj.x
        this.points.push(obj)
      }
    },
    //电脑设备事件
    mouseUp(ev) {
      ev = ev || event
      ev.preventDefault()
      if (1) {
        let obj = {
          x: ev.offsetX,
          y: ev.offsetY
        }
        this.canvasTxt.beginPath()
        this.canvasTxt.moveTo(this.startX, this.startY)
        this.canvasTxt.lineTo(obj.x, obj.y)
        this.canvasTxt.stroke()
        this.canvasTxt.closePath()
        this.points.push(obj)
        this.points.push({ x: -1, y: -1 })
        this.isDown = false
      }
    },
    //移动设备事件
    touchEnd(ev) {
      ev = ev || event
      ev.preventDefault()
      if (ev.touches.length == 1) {
        let obj = {
          x: ev.targetTouches[0].clientX,
          y: ev.targetTouches[0].clientY - 48
        }
        this.canvasTxt.beginPath()
        this.canvasTxt.moveTo(this.startX, this.startY)
        this.canvasTxt.lineTo(obj.x, obj.y)
        this.canvasTxt.stroke()
        this.canvasTxt.closePath()
        this.points.push(obj)
        this.points.push({ x: -1, y: -1 })
      }
    },
    //重写
    overwrite() {
      this.canvasTxt.clearRect(0, 0, this.$refs.canvasF.width, this.$refs.canvasF.height)
      this.points = []
    }
  }
}
</script>

<style scoped>
.skip {
  width: 100px;
  height: 40px;
}
.signatureBox {
  position: absolute;
  top: 80px;
  left: 0px;
  width: 80%;
  box-sizing: border-box;
  overflow: hidden;
  z-index: 100;
  display: flex;
  flex-direction: column;
  transform: translateX(10%);
  border: 1px solid #f5f5f5;
}
.visaDetailTop {
  width: 100%;
  padding: 5px;
  box-sizing: border-box;
  z-index: 2;
  border-bottom: 1px solid #e5e5e5;
}
.visaDetailTop p {
  margin: 0px;
  text-align: center;
  color: #000;
  font-size: 1em;
  position: relative;
}
p.visaTitle {
  width: 100%;
  position: absolute;
  top: 5px;
  left: 0px;
  text-align: center;
  font-size: 1.2em;
}
.btnBack {
  display: block;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 1;
  background: transparent;
  border-color: transparent;
  outline: none;
}
.btnDaoHang {
  display: block;
  position: absolute;
  left: 0px;
  top: 0px;
  height: 2.2em;
  width: 2em;
  z-index: 1;
  background: transparent;
  border-color: transparent;
  outline: none;
}
.visaDetailTop p span {
  color: #fff;
  font-size: 1.2em;
}
.visaDetailTop p:first-of-type {
  float: left;
}
.visaDetailTop p:nth-of-type(2) {
  float: right;
}
.canvasBox {
  padding: 10px 5px;
  box-sizing: border-box;
  flex: 1;
}
canvas {
  margin: 0;
  padding: 0;
  width: 100%;
}
.btnBox {
  position: absolute;
  top: 20px;
  right: 20px;
  height: 30px;
  padding: 5px;
  text-align: right;
  line-height: 30px;
  outline: none;
}
.btnBox button:first-of-type,
.btnBox button:last-of-type {
  border: 1px solid #4091fb;
  background: transparent;
  border-radius: 100%;
  width: 42px;
  height: 42px;
  color: #fff;
  background: #4091fb;
  background-size: 20px;
  text-align: center;
  outline: none;
}
/* @media only screen and (min-width: 750px) {
  .signatureBox {
    position: absolute;
    top: 60px;
    left: 0px;
    width: 100%;
    min-height: 100%;
    box-sizing: border-box;
    overflow: visible;
  }
} */
</style>
