<template>
  <div>
    <van-popup v-model="show" round position="bottom" :style="{ height: '40%' }" class="popup">
      <header>
        <span>个人签章</span>
        <img src="../../assets/images/close.png" @click="returnButton" />
      </header>
      <div class="chooseSeal">
        <div class="hand-content" v-if="!enterprise">
          <div class="show-seal-img">
            <div v-if="enableHandSign == 'YES'">
              <div class="addSign" v-if="!selectedSignImg" @click="addHand">
                <van-icon name="plus" class="add" />
                <p class="add-text">手写签章</p>
              </div>
              <div class="addSign" v-else @click="chooseClickSign" :class="{ active: !clickFile }">
                <img :src="selectedSignImg" alt />
              </div>
              <p class="name">手写签章</p>
              <!-- <p class="name-tip">（建议<span class="rotate">旋转</span>屏幕横屏签署）</p> -->
            </div>
            <div v-if="imageFile">
              <div class="addSign" @click="chooseClickFile" :class="{ active: clickFile }">
                <img :src="'data:image/png;base64,' + imageFile" alt />
              </div>
              <p class="name">默认签章</p>
            </div>
          </div>
          <div class="choose-btn">
            <!-- <van-button type="default" size="normal" class="reset" @click="returnButton">关 闭</van-button> -->
            <div class="btn  f-c" @click="addHandWrite" v-if="enableHandSign == 'YES'">重 写</div>
            <div class="btn use f-c" @click="useSeal">使 用</div>
          </div>
        </div>
        <div class="hand-content" v-else>
          <div class="show-seal-img">
            <!-- <van-col span="12">
          <div class="addSeal" v-if="!selectedSealImg">
            <van-uploader :max-count="1" :preview-image="false" :after-read="afterRead">
              <p class="add-text seal-text">
                <van-icon name="add-o" class="add" />
                <br />
              </p>
              <p class="add-text">点击上传自定义印章</p>
            </van-uploader>
          </div>
          <div class="addSeal" v-else @click="chooseClickSign" :class="{'active':!clickFile}">
            <img :src="selectedSealImg" alt />
          </div>
          <p class="name">上传印章</p>
        </van-col>-->
            <div v-if="imageFile">
              <div class="addSeal" @click="chooseClickFile" :class="{ active: clickFile }">
                <img :src="'data:image/png;base64,' + imageFile" alt />
              </div>
              <p class="name">默认签章</p>
            </div>
          </div>
          <div class="choose-btn">
            <!-- <van-button type="default" size="normal" class="reset" @click="upLoadAgin">重新上传</van-button> -->
            <!-- <van-button type="info" size="normal" @click="useSeal">使 用</van-button> -->
          </div>
        </div>
      </div>
    </van-popup>
    <div class="handwrite-warp" v-if="isShowHandwrite">
      <handwriting :showColor="false" mode="two" @onComplete="saveImageInfo" @clickReturn="clickReturn"></handwriting>
    </div>
  </div>
</template>
<script>
import handwriting from "./handwrite"
export default {
  data() {
    return {
      signImgChild: this.signImg,
      clickFile: true,
      selectedSignImg: "",
      selectedSealImg: "",
      isShowHandwrite: false,
      show: false
    }
  },
  props: {
    imageFile: {
      type: String
    },
    signImg: {
      type: String
    },
    enableHandSign: {
      type: String
    },
    enterprise: {
      type: Boolean
    }
  },
  components: {
    handwriting
  },
  mounted() {
    if (!this.enterprise) {
      this.selectedSignImg = sessionStorage.getItem("selectedSignImg")
    } else {
      this.selectedSealImg = sessionStorage.getItem("selectedSealImg")
    }
    let defaultImg = "data:image/png;base64," + this.imageFile
    if (this.imageFile && (this.signImgChild == defaultImg || this.signImgChild == "")) {
      this.clickFile = true
    } else {
      this.clickFile = false
    }
  },
  methods: {
    returnButton() {
      this.$emit("returnButton")
    },
    addHandWrite() {
      if (this.selectedSignImg) {
        this.show = false
        this.isShowHandwrite = true
      } else {
        this.$toast.fail("您还没有添加签名")
      }
    },
    addHand() {
      this.show = false
      this.isShowHandwrite = true
    },
    saveImageInfo(imageSrc) {
      this.isShowHandwrite = false
      // this.show = true
      this.clickFile = false
      this.selectedSignImg = imageSrc
      sessionStorage.setItem("selectedSignImg", this.selectedSignImg)
      this.useSeal()
    },
    clickReturn() {
      this.isShowHandwrite = false
      this.show = true
    },
    upLoadAgin() {
      this.signImgChild = ""
      this.selectedSealImg = ""
      this.clickFile = true
      sessionStorage.clear()
    },
    useSeal() {
      if (this.clickFile) {
        this.signImgChild = "data:image/png;base64," + this.imageFile
      } else {
        if (!this.enterprise) {
          this.signImgChild = this.selectedSignImg
        } else {
          this.signImgChild = this.selectedSealImg
        }
      }
      if (this.signImgChild) {
        this.$emit("useSeal", this.signImgChild)
      } else {
        this.$toast.fail("签名为空")
      }
    },
    chooseClickSign() {
      this.clickFile = false
    },
    chooseClickFile() {
      this.clickFile = true
    },
    afterRead(file) {
      this.selectedSealImg = file.content
      sessionStorage.setItem("selectedSealImg", file.content)
    }
  }
}
</script>
<style lang="scss" scoped>
.f-c {
  display: flex;
  justify-content: center;
  align-items: center;
}
header {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  border: 1px solid #eef0f4;
  span {
    font-weight: 500;
    font-size: 16px;
    color: #070f29;
  }
  img {
    width: 12px;
    height: 12px;
  }
}
.chooseSeal {
  // height: 100%;
  overflow: hidden;
  position: relative;
}
.show-seal-img {
  padding: 0 16px;
  margin-top: 20px;
  display: flex;
  .addSign {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 130px;
    height: 62px;
    border: 1px solid #eef0f4;
    border-radius: 4px;
    padding: 10px;
    box-sizing: border-box;
    margin-right: 16px;
    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
  .addSeal {
    width: 120px;
    height: 120px;
    border: 1px solid #c1c1c1;
    padding: 10px;
    margin: 0 auto;
    img {
      display: block;
      width: 100%;
      height: 100%;
    }
    .seal-text {
      margin-top: 33px;
    }
  }
  .active {
    border: 1px solid #4688f5;
  }
  .add {
    font-size: 16px;
    color: #777c94;
    margin-right: 5px;
  }
  .add-text {
    color: #777c94;
    font-size: 14px;
  }
  .name {
    text-align: center;
    margin-top: 10px;
    margin-bottom: 5px;
    font-weight: 400;
    font-size: 12px;
    color: #24262a;
  }
  .name-tip {
    text-align: center;
    color: #bfbfbf;
    font-size: 12px;
    .rotate {
      color: rgb(249, 166, 76);
    }
  }
}

.choose-btn {
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88px;
  width: 100%;
  background: #ffffff;
  box-shadow: 0 -1px 8px 0 rgba(224, 224, 224, 0.6);
  box-sizing: border-box;
  padding: 0 16px 24px;

  .btn {
    width: 165px;
    height: 46px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 16px;
    border: 1px solid #cccccc;
    box-sizing: border-box;
    color: #6a6f7f;
  }
  .use {
    background: #4f71ff;
    color: #fff;
    border: none;
  }
}
.handwrite-warp {
  position: fixed;
  z-index: 99;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
}
</style>
