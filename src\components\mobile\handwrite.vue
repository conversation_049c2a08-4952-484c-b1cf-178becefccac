<template>
  <div class="handwriting-board" :class="{ direction, landbox: istransverse }">
    <div class="hand-content">
      <div class="board-box" :class="{ landscape: istransverse }">
        <img v-if="filePath" :src="filePath" />
        <canvas
          v-else
          ref="boardCanvas"
          :width="canvasWidth"
          :height="canvasHeight"
          @touchstart="onTouchStart"
          @touchmove="onTouchMove"
          @touchend="onTouchEnd"
        ></canvas>
      </div>
    </div>
    <div>
      <div class="hand-box">
        <div class="color-box" v-if="showColor">
          <div
            v-for="(item, index) in colors"
            :key="index"
            class="color-item"
            :class="{ active: item.active }"
            :style="{ background: item.color }"
            @click="clickColorItem(item)"
          ></div>
        </div>

        <div class="btn f-c" v-if="!filePath" @click="clickReturn">返回</div>
        <div class="btn f-c" @click="clickReset">重写</div>
        <div class="btn use f-c" v-if="!filePath" @click="clickFinish">使用</div>
        <!-- <button v-if="mode === 'two'" @click="clickOrientation">{{direction === 'horizontal' ? '返回' : '横版'}}</button> -->
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      istransverse: false, //是否横屏
      direction: "", // 画板方向 vertical horizontal
      calHeight: 170, //动态计算高度，根据比例调整
      colors: [
        { color: "black", active: true },
        { color: "red", active: false },
        { color: "blue", active: false }
      ], // 可选颜色
      ctx: "", // canvas对象
      canvasWidth: "", // canvas宽度
      canvasHeight: "", // canvas高度
      lineColor: "black", // 线条的颜色
      lineWidth: 3, // 线条宽度
      filePath: "", // 生成的图片地址
      touchMove: false
    }
  },
  props: {
    showColor: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: "full" // 三种模式 缩小版mini,全屏版full,两种模式都有two
    }
  },
  mounted() {
    document.title = "在下方设置手写签名"
    const t = this
    // 如果mode为全屏模式，设置direction为horizontal
    if (t.mode === "full") {
      t.direction = "horizontal"
    }

    //横竖屏切换
    window.onorientationchange = function() {
      t.clickReset()
      if (window.orientation == 90 || window.orientation == -90) {
        //横屏
        console.info("横屏了")
        // t.calHeight = 220
        t.istransverse = true
      } else {
        //竖屏
        // t.calHeight = 180
        t.istransverse = false
      }
    }
    t.initCanvas()
  },
  methods: {
    /**
     * 初始化画板，获取canvas节点对象，设置画板的宽高
     * 不能在此方法中设置线条宽度样式，否则无效
     */
    initCanvas() {
      if (window.orientation == 90 || window.orientation == -90) {
        //横屏
        console.info("横屏了")
        // t.calHeight = 220
        this.istransverse = true
      }
      this.$nextTick(() => {
        // 获取到当前canvas节点的信息，包含宽，高，top，left等
        let boardCanvas = this.$refs.boardCanvas // 获取canvas元素
        console.log(boardCanvas)
        this.canvasWidth = boardCanvas.offsetWidth // 设置画板宽度
        this.canvasHeight = boardCanvas.offsetHeight // 设置画板高度
        // canvas基础设置，线条设置
        this.ctx = boardCanvas.getContext("2d")
        this.ctx.beginPath()
        boardCanvas.addEventListener("touchstart", function(event) {
          event.preventDefault() // 阻止在canvas画布上签名的时候页面跟着滚动
        })
        boardCanvas.addEventListener("touchmove", event => {
          event.preventDefault() // 阻止在canvas画布上签名的时候页面跟着滚动
        })
      })
    },
    /**
     * 绘制笔触
     */
    handleDraw(targetX, targetY) {
      this.ctx.lineTo(targetX, targetY) // 将笔触移到当前点击点
      this.ctx.stroke()
    },
    /**
     * 触摸开始
     * 获取当前点击点的坐标
     * 设置线条颜色，宽度，样式等
     */
    onTouchStart(e) {
      console.log(e)
      this.touchMove = true
      let offsetLeft = e.target.offsetLeft // 获取canvas距离页面左边的距离
      let offsetTop = e.target.offsetTop // 获取canvas距离页面顶部的距离
      let targetX = e.touches[0].clientX - offsetLeft
      let targetY = e.touches[0].clientY - offsetTop
      this.ctx.beginPath()
      this.ctx.strokeStyle = this.lineColor // 设置线条颜色
      this.ctx.lineWidth = this.lineWidth // 设置线条的宽度
      this.ctx.lineCap = "round" // 设置线条的端点的样式，设为圆弧形
      this.ctx.lineJoin = "round" // 设置线条的连接点的样式，设为弧形
      this.handleDraw(targetX, targetY)
    },
    /**
     * 触摸过程中
     * 获取并计算当前点击点的坐标，绘制线条
     */
    onTouchMove(e) {
      let offsetLeft = e.target.offsetLeft // 获取canvas距离页面左边的距离
      let offsetTop = e.target.offsetTop // 获取canvas距离页面顶部的距离
      let targetX = e.touches[0].clientX - offsetLeft
      let targetY = e.touches[0].clientY - offsetTop
      this.handleDraw(targetX, targetY)
    },
    /**
     * 触摸结束
     */
    onTouchEnd(e) {},
    /**
     * 颜色模块点击事件，切换点击的颜色状态
     */
    clickColorItem(e) {
      this.colors.forEach(item => {
        item.active = false
      })
      e.active = true
      this.lineColor = e.color // 设置当前笔触颜色
    },
    /**
     * 重写按钮点击事件，清空画板内容
     */
    clickReset() {
      // 清空图片
      this.filePath = ""
      this.touchMove = false
      // 清空画板
      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
      // 重新设置canvas画板节点对象，否则绘画会出问题,这里异步操作，否则绘画有误
      setTimeout(() => {
        this.initCanvas()
      }, 100)
    },
    clickReturn() {
      this.$emit("clickReturn")
      document.title = "合同信息"
    },
    /**
     * 点击完成，生成画板图片，隐藏canvas画板区域，将生成的图片显示出来
     */
    clickFinish() {
      if (this.touchMove) {
        // 将canvas转成临时图片数据保存
        this.filePath = this.$refs.boardCanvas.toDataURL()
        this.$emit("onComplete", this.filePath) // 将生成的图片传给父组件
        document.title = "合同信息"
      } else {
        this.$toast.fail("您没有设置任何签名")
      }
    },
    /**
     * 点击“横版”“返回”,如果当前是缩小版，改为横版，否则返回为缩小版
     * 清空画板内容
     */
    clickOrientation() {
      if (this.direction === "horizontal") {
        this.direction = ""
      } else {
        this.direction = "horizontal"
      }
      // 清空画板内容后后修改样式, 重置canvas
      this.clickReset()
    }
  }
}
</script>
<style lang="scss" scoped>
.f-c {
  display: flex;
  justify-content: center;
  align-items: center;
}
.hand-content {
  padding: 20px;
}
.board-box {
  width: 100%;
  height: 170px;
  overflow: hidden;
  box-sizing: border-box;
  background: #fff;
  border: 1px solid #eef0f4;
  border-radius: 8px;
  box-sizing: border-box;
  canvas,
  img {
    width: 100%;
    height: 100%;
  }
}
.landscape {
  height: 60vh !important;
}

.hand-box {
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  .color-box {
    display: flex;
    .color-item {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin: 0px 5px;
      transition: 0.3s;
      &.active {
        position: relative;
        transform: scale(0.8);
        &::after {
          content: "";
          position: absolute;
          width: 100%;
          height: 100%;
          border: 1px solid #ccc;
          border-radius: 50%;
          transform: scale(1.25);
          box-sizing: border-box;
        }
      }
    }
  }
  .btn {
    width: 106px;
    height: 44px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 16px;
    border: 1px solid #cccccc;
    box-sizing: border-box;
    color: #6a6f7f;
  }
  .use {
    background: #4f71ff;
    color: #fff;
    border: none;
  }
}
.landbox {
  .hand-content {
    padding: 20px 40px;
  }
  .hand-box {
    padding: 20px;
    justify-content: flex-end;
    box-shadow: 0 -1px 8px 0 rgba(224, 224, 224, 0.6);
    .btn {
      margin-right: 12px;
    }
  }
}

// 水平状态样式
.horizontal {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0px;
  left: 0px;
  background: #ffffff;
  border: 1px solid #c1c1c1;
  display: flex;
  align-items: center;
  flex-flow: row-reverse;
  .board-box {
    width: calc("100% - 100px");
    height: 90vh;
  }

  .handle-box {
    height: 90vh;
    width: 25px;
    flex-direction: column;

    padding: 0px 10px;
    .color-box {
      flex-direction: column;
      .color-item {
        margin: 10px 0px;
      }
    }
    button {
      transform: rotate(90deg);
      margin: 40px 0px 10px;
    }
  }
}
</style>
