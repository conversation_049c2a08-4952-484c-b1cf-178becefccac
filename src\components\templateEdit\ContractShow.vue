<template>
  <div class="contract-show" style="user-select: none;">
    <!--pdf预览区-->

    <div class="contract-warp" ref="refContractWarp">
      <el-scrollbar style="height:100%" class="no-xScroll">
        <div class="countPage card-box fixed-top">
          <!--<div>{{controlListBack[3]}}</div>-->
          <div class="count"></div>
          <el-button size="small" type="text" @click="pageChange(0)" :disabled="currentPdfPage === 1">
            上一页
            <i class="el-icon-arrow-up"></i>
          </el-button>
          <el-button size="small" type="text" @click="pageChange(1)" :disabled="currentPdfPage === pdfPictures.length">
            下一页
            <i class="el-icon-arrow-down"></i>
          </el-button>
          <span style="margin: 0 30px;">{{ currentPdfPage }}/{{ pdfPictures.length }} 页</span>
          <span style="margin: 0 50px;">
            跳转至
            <el-input v-model="tempCurrentPdfPage" class="page-jump" size="small"></el-input>页
            <img src="../../assets/images/right.png" width="14px" @click="handleGotoPage(false)" class="right" />
          </span>
        </div>
        <div
          class="pdf-warp"
          @mouseenter.prevent="handleDragContainerEnter"
          @mouseleave.prevent="handleDragContainerLeave"
        >
          <!--<div>{{controlList}}</div>-->
          <!--拖拽的控件预览-->
          <div class="div-drag-tempnode" v-if="contactObj.dragTempnodeShow && mouseXy.x > 0" :style="dragTempnodeStyle">
            <!--<div v-if="contactObj.controlType==='FIELD_CONTROL'" style="width: 130px;height: 30px;">自定义控件{{controlListBack.filter(it=>it.type==='FIELD_CONTROL').length}}</div>-->
            <div v-if="contactObj.controlType === 'FIELD_CONTROL'" style="width: 130px;height: 30px;">
              {{ contactObj.currentSelectRole.name }}
            </div>
            <div v-if="contactObj.controlType === 'DATE_CONTROL'" style="width: 130px;height: 30px;">签署日期</div>
            <div v-if="contactObj.controlType === 'SIGN_CONTROL'" style="width: 110px;height: 35px;">
              <img src="../../assets/images/qianming.png" alt style="width: 100%;" />
            </div>
            <div v-if="contactObj.controlType === 'SEAL_CONTROL'" style="width: 152px;height: 152px;">
              <img src="../../assets/images/gongzhang.png" alt style="width: 100%;" />
            </div>
          </div>
          <!--遍历出控件列表-->
          <div v-if="isShowControl">
            <vue-draggable-resizable
              v-for="(item, index) in controlListBack"
              v-if="item.belong && item.belong === currentPdfPage && !item.isDelete"
              :key="index"
              :active.sync="item.isActive"
              :prevent-deactivation="true"
              parent=".pdf-warp"
              :w="item.width"
              :min-width="50"
              :h="item.height"
              :min-height="20"
              :x="item.xAxis"
              :y="item.yAxis"
              :draggable="!isReadonly"
              :resizable="item.type === 'FIELD_CONTROL'"
              @resizing="handleResizing(arguments, index)"
              @dragging="handleDragging(arguments, index)"
              @activated="handleActivated(item)"
              @deactivated="currentActiveControl = { type: '' }"
              :class="{
                'height-auto': isReadonly && item.type === 'SIGN_CONTROL',
                signRead:
                  item.type != 'SEAL_CONTROL' &&
                  item.type != 'SIGN_CONTROL' &&
                  item.type != 'DATE_CONTROL' &&
                  isOnlySign
                    ? signRead
                    : ''
              }"
              :style="{ zIndex: item.isActive ? 999 : '1' }"
              class-name-handle="my-handle-class"
            >
              <!-- ||item.type=='SIGN_CONTROL'||item.type=='FIELD_CONTROL' -->
              <!-- @deactivated="handleActivated({controlInfo:{name:'',type:''}})" -->
              <!-- <div class="control-info ellipsis" v-if="item.isActive && !isOnlySign">{{ getInfoContent(item) }}</div> -->
              <!-- <div
                class="control-info ellipsis"
                v-if="
                  (item.isActive && item.type == 'SEAL_CONTROL' && isOnlySign) ||
                    (item.isActive && item.type == 'SIGN_CONTROL' && isOnlySign)
                "
              >
                {{ getInfoContent(item) }}
              </div>-->
              <!--只读模式下不显示删除按钮-->
              <div class="btn-close" v-if="!isReadonly" @click="handleDeleteControl(index)">
                <i class="el-icon-circle-close"></i>
              </div>
              <div class="control-content" style="width: 100%;height: 100%;">
                <!--自定义控件控件 在canEdit为true时 可编辑 但 选择多个人&&有关联项时 不可编辑-->
                <span v-if="item.type === 'FIELD_CONTROL'">
                  <span v-if="isReadonly && canEdit">
                    <!-- <span v-if="item.userName.length >= 2 && item.referenceField !== ''">
                      {{
                        item.operate !== "SEAL"
                          ? referenceFieldData[item.referenceField]
                          : referenceFieldDataQiye[item.referenceField]
                      }}
                    </span>-->
                    <textarea
                      disabled
                      class="input-control"
                      :style="{
                        border:
                          validateRequired && (item.value || item.referenceField) === '' ? '1px solid red' : 'none'
                      }"
                      v-model="item.value"
                      :placeholder="item.name"
                      maxlength="100"
                    ></textarea>
                  </span>
                  <span v-if="!isReadonly">{{ item.name }}</span>
                </span>
                <div v-if="item.type === 'DATE_CONTROL'">签署日期</div>

                <div v-if="item.type === 'SIGN_CONTROL'">
                  <img v-if="signImage.sign" :src="signImage.sign" alt style="width: 100%;display: block;" />
                  <img v-else src="../../assets/images/qianming.png" alt style="width: 100%;" />
                </div>
                <div v-if="item.type === 'SEAL_CONTROL'">
                  <img v-if="signImage.seal" :src="signImage.seal" alt style="width: 100%;display: block;" />
                  <img v-else src="../../assets/images/gongzhang.png" alt style="width: 100%;" />
                </div>
              </div>
              <!--<label>-->
              <!--<input type="text" style="width: 100%;height:100%;background-color: transparent;">-->
              <!--</label>-->
            </vue-draggable-resizable>
          </div>
          <div class="movie-info">
            <img
              ref="refPdfImg"
              :src="pdfPictures[currentPdfPage - 1]"
              @click="handlePdfImgClick"
              @load="setControlList(1)"
            />
          </div>
        </div>
      </el-scrollbar>
    </div>
    <!--控件属性操作区-->
    <div class="control-warp card-box" v-if="!isReadonly">
      <!--已经选控件列表-->
      <div
        class="selected"
        v-if="currentActiveControl.type !== 'FIELD_CONTROL' && currentActiveControl.type !== 'DATE_CONTROL'"
      >
        <div>
          <img
            src="../../assets/images/noControlList.png"
            alt
            v-if="controlListBack.filter(it => !it.isDelete).length == 0"
          />
        </div>
        <div class="title" style="text-align: left" v-if="controlListBack.filter(it => !it.isDelete).length != 0">
          <span>已选控件：</span>
        </div>
        <div
          class="control-item"
          v-for="(item, index) in controlListBack.filter(it => !it.isDelete)"
          :key="index"
          @click="handleActivated(item)"
          :class="{ 'control-item--active': item.isActive }"
        >
          <div v-if="item.type === 'FIELD_CONTROL'">{{ item.name }}</div>
          <div v-if="item.type === 'DATE_CONTROL'">签署日期</div>
          <div v-if="item.type === 'SIGN_CONTROL'">签署</div>
          <div v-if="item.type === 'SEAL_CONTROL'">企业公章</div>
        </div>
      </div>
      <!--自定义控件设置-->
      <div
        class="diy-set"
        v-if="currentActiveControl.type === 'FIELD_CONTROL' || currentActiveControl.type === 'DATE_CONTROL'"
        body-style="padding:0"
      >
        <div class="title" style="text-align: left">
          <span>编辑自定义文本</span>
        </div>
        <div class="line"></div>
        <div style="text-align: left">
          <div class="label"><span style="color:red;">*</span> 名称：</div>
          <el-input
            disabled
            v-model="currentActiveControl.name"
            @change="changeClear"
            @focus="focusName"
            placeholder="请输入名称"
            maxlength="20"
          ></el-input>
          <div class="label"><span style="color:red;">*</span> 字号：</div>
          <el-select v-model="currentActiveControl.fontSize" placeholder="请选择" @change="fontSizeChange">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
      </div>
    </div>
    <div style="clear: both;"></div>
    <el-dialog :visible.sync="isShowFieldDialog" v-if="isShowFieldDialog">
      <el-radio-group v-model="currentActiveControl.referenceField" class="radios">
        <el-radio v-for="(value, key) in referenceFieldData" :key="key" :label="key">{{ value }}</el-radio>
      </el-radio-group>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="
            currentActiveControl.referenceField = ''
            isShowFieldDialog = false
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="isShowFieldDialog = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import VueDraggableResizable from "vue-draggable-resizable"
import "vue-draggable-resizable/dist/VueDraggableResizable.css"
import { referenceFieldData, referenceFieldDataQiye } from "../../util/constData"

export default {
  components: {
    VueDraggableResizable
  },
  props: {
    pdfPictures: {
      type: Array,
      default: () => {
        return []
      }
    },
    controlList: {
      type: Array,
      default: () => {
        return []
      }
    },
    isReadonly: {
      type: Boolean,
      default: false
    },
    isOnlySign: {
      type: Boolean,
      default: false
    },
    canEdit: {
      type: Boolean,
      default: false
    },
    signImage: {
      type: Object,
      default: () => {
        return { sign: "", seal: "" }
      }
    },
    contactObj: {
      type: Object,
      default: () => {
        return {}
      }
    }
    // currentSelectRole:Object
    // currentPdfPage:{type:Number,default:1}
  },
  data() {
    return {
      referenceFieldData: referenceFieldData,
      referenceFieldDataQiye: referenceFieldDataQiye,
      currentPdfPage: 1,
      tempCurrentPdfPage: "",
      currentActiveControl: {
        type: ""
      },
      mouseXy: {
        x: 0,
        y: 0
      },
      isShowFieldDialog: false,
      isfixdTop: false,
      validateRequired: false,
      controlListBack: [],
      isShowControl: false,
      defaultValue: "",
      options: [
        {
          value: "12",
          label: "小四号"
        },
        {
          value: "14",
          label: "四号"
        },
        {
          value: "15",
          label: "小三号"
        },
        {
          value: "16",
          label: "三号"
        },
        {
          value: "18",
          label: "小二号"
        },
        {
          value: "22",
          label: "二号"
        },
        {
          value: "24",
          label: "小一号"
        },
        {
          value: "26",
          label: "一号"
        }
      ],
      signRead: "signRead"
    }
  },
  watch: {
    pdfPictures() {
      this.currentPdfPage = 1
    }
  },
  computed: {
    dragTempnodeStyle() {
      const t = this
      return {
        left: t.mouseXy.x + "px",
        top: t.mouseXy.y + "px"
      }
    },
    plusShow() {
      const t = this
      return t.currentActiveControl.value === "" && t.currentActiveControl.referenceField === ""
    }
  },
  created() {
    console.log("created")
  },
  mounted() {
    if (this.controlList.length > 0) {
      let tempList = [...this.controlList]
      tempList.forEach(it => {
        it["isActive"] = false
        it["isDelete"] = false
      })
      this.$set(this, "controlListBack", tempList)
      // this.setControlList(1)
    }
  },
  methods: {
    //pdf翻页
    pageChange(flag) {
      let t = this
      switch (flag) {
        case 0: {
          t.currentPdfPage = t.currentPdfPage <= 1 ? 1 : --t.currentPdfPage
          break
        }
        case 1: {
          t.currentPdfPage = t.currentPdfPage >= t.pdfPictures.length ? t.pdfPictures.length : ++t.currentPdfPage
          break
        }
      }
    },
    handleGotoPage(page) {
      const t = this
      // t.validateControlContent(true);
      page ? (t.tempCurrentPdfPage = page.toString()) : ""
      const tempNum = parseInt(t.tempCurrentPdfPage)
      if (!isNaN(tempNum) && tempNum <= t.pdfPictures.length && tempNum > 0 && t.pdfPictures.length >= 1) {
        t.currentPdfPage = parseInt(t.tempCurrentPdfPage)
      } else {
        this.$message({
          message: "请输入正确的页码",
          type: "warning"
        })
      }
    },
    focusName(event) {
      this.defaultValue = event.target.value
    },
    changeClear(val) {
      if (val == "") {
        this.currentActiveControl.name = this.defaultValue
      }
    },
    //删除某个控件
    handleDeleteControl(cindex) {
      const t = this
      t.controlListBack[cindex].isDelete = true
      //清空所有控件激活状态
      t.controlListBack.forEach(it => (it.isActive = false))
      t.currentActiveControl = { type: "" }
    },
    //调整控件宽高
    handleResizing(args, index) {
      const t = this
      console.info("handleResizing-----", args, index)
      t.controlListBack[index].width = args[2]
      t.controlListBack[index].height = args[3]
    },
    //调整控件xy坐标
    handleDragging(args, index) {
      const t = this
      console.info("handleDragging", args, index)
      t.controlListBack[index].xAxis = args[0]
      t.controlListBack[index].yAxis = args[1]
    },
    //进入拖动区
    handleDragContainerEnter(e) {
      const t = this
      // this.dragTempnodeShow = true;
      //绑定拖动&鼠标抬起事件
      window.addEventListener("mousemove", t.handleUpdateMouseXy)
      window.addEventListener("mouseup", t.handleMouseup)
      console.info("enter-----")
    },
    handleUpdateMouseXy($event) {
      // console.info(111111111,$event)
      if (this.contactObj.dragTempnodeShow) {
        this.mouseXy.x = $event.clientX
        this.mouseXy.y = $event.clientY
      }
    },
    // 鼠标抬起
    handleMouseup(e) {
      const t = this
      console.info("up----")
      // console.info('t.dragTempnodeShow----', t.contactObj.dragTempnodeShow)
      // console.info('t.mouseXy----', t.mouseXy.x)
      //当移动过
      if (t.contactObj.dragTempnodeShow && t.mouseXy.x > 0) {
        // 设置不同控件的初始宽高
        const cType = t.contactObj.controlType
        let cw = 130
        let ch = 30
        switch (cType) {
          case "DATE_CONTROL": {
            cw = 130
            ch = 30
            break
          }
          case "SIGN_CONTROL": {
            cw = 110
            ch = 35
            break
          }
          case "SEAL_CONTROL": {
            cw = 152
            ch = 152
            break
          }
        }
        //读取预设值
        let tempName = ""
        let presetInfo = t.contactObj.presetControlInfo
        if (presetInfo) {
          tempName = presetInfo.name
        } else {
          console.log(cType)
          if (cType === "FIELD_CONTROL") {
            tempName = t.contactObj.currentSelectRole.name
          }
        }
        // 获取当前合同图片的宽高 单位px
        const pdfWidth = 800
        const pdfHeight = t.$refs["refPdfImg"].offsetHeight
        // 创建控件初始状态
        setTimeout(() => {
          const tempControl = {
            ...t.contactObj.currentSelectRole,
            belong: t.currentPdfPage,
            type: cType,
            isPreset: !!presetInfo,
            ...{
              width: cw,
              height: ch,
              //防止超出右边缘和下边缘
              xAxis: e.offsetX + cw > pdfWidth ? pdfWidth - cw : e.offsetX,
              yAxis: e.offsetY + ch > pdfHeight ? pdfHeight - ch : e.offsetY,
              // name: tempName,
              // value: "",
              fontFamily: "SIMSUN",
              fontSize: "12" //默认小四号
            },
            isActive: true,
            isDelete: false
          }
          //清空所有控件激活状态
          t.controlListBack.forEach(it => (it.isActive = false))
          t.controlListBack.push(tempControl)
          t.$nextTick(() => {
            //设置为当前选中的控件
            t.currentActiveControl = tempControl
          })
        }, 100)
      }
      // console.info("---controlList----", JSON.stringify(t.controlListBack));
      t.contactObj.dragTempnodeShow = false
      console.log(t.controlListBack)
    },
    fontSizeChange(val) {
      console.log(val)
    },
    //离开拖动区
    handleDragContainerLeave(e) {
      const t = this
      //重置tempNode状态
      t.resetTempnode()
      console.info("leave-----")
    },
    //移除鼠标事件 归零xy
    resetTempnode() {
      const t = this
      // t.dragTempnodeShow = false;
      window.removeEventListener("mousemove", this.handleUpdateMouseXy)
      window.removeEventListener("mouseup", this.handleMouseup)
      t.mouseXy.x = t.mouseXy.y = 0
    },
    //处理坐标及宽高的转换
    setControlList(flag) {
      // flag为1 系数 转为 像素
      const t = this
      // 获取当前合同图片的宽高 单位px
      const pdfWidth = 800
      let pdfHeight = 1135 //t.$refs['refPdfImg'].offsetHeight;

      if (flag) {
        //兼容拖拽插件 created中操作数据转换会造成控件如果设置parent=true 则x和height为负数的问题
        // setTimeout(() => {
        var img = new Image()
        img.src = t.pdfPictures[0]
        img.onload = function() {
          // 根据宽高比计算 宽度为800时高度的值
          pdfHeight = 800 / (img.width / img.height)
          console.info("pdfWidth-pdfHHH", pdfWidth, pdfHeight)

          if (t.controlListBack.length > 0) {
            // console.info('controlList length大于0')
            if (parseFloat(t.controlListBack[0].width) < 1) {
              let tempControlList = JSON.parse(JSON.stringify(t.controlListBack))
              tempControlList.forEach(item => {
                item.xAxis = item.xAxis * pdfWidth
                item.yAxis = item.yAxis * pdfHeight
                item.width = item.width * pdfWidth
                item.height = item.height * pdfHeight
                item.fontSize = parseInt(item.fontSize * pdfWidth)
                t.options.forEach(it => {
                  if (it.value == item.fontSize) {
                    item.fontSize = it.label
                  }
                })
              })
              t.$set(t, "controlListBack", tempControlList)
            }
          }
          t.isShowControl = true
        }
        // }, 100)
      } else {
        // flag为0 克隆 像素 转为 系数
        let pdfHeight = t.$refs["refPdfImg"].offsetHeight
        // console.info('pdfWidth-pdfHHH',pdfWidth,pdfHeight)
        let tempControlList = JSON.parse(JSON.stringify(t.controlListBack.filter(it => !it.isDelete)))
        tempControlList.forEach(item => {
          item.xAxis = parseFloat((item.xAxis / pdfWidth).toFixed(6))
          item.yAxis = parseFloat((item.yAxis / pdfHeight).toFixed(6))
          item.width = parseFloat((item.width / pdfWidth).toFixed(6))
          item.height = parseFloat((item.height / pdfHeight).toFixed(6))
          t.options.forEach(it => {
            if (it.label == item.fontSize) {
              item.fontSize = it.value
            }
          })
          item.fontSize = parseFloat((item.fontSize / pdfWidth).toFixed(6))
        })
        return tempControlList
      }
    },
    handlePdfImgClick() {
      const t = this
      t.controlListBack.forEach(it => (it.isActive = false))
    },
    handleActivated(item) {
      // console.info("item-----", JSON.stringify(item));
      const t = this
      setTimeout(() => {
        this.currentActiveControl = item
        t.controlListBack.forEach(it => (it.isActive = it == item))
        //自动翻页
        t.currentPdfPage = item.belong
      }, 1)
    },
    //开启或关闭控件文本框验证
    validateControlContent(isRequired) {
      const t = this
      t.validateRequired = isRequired
      if (isRequired) {
        return t.controlListBack.filter(it => (it.value || it.referenceField) === "").length === 0
      }
    },
    //控件信息内容计算
    getInfoContent(item) {
      let result = item.stepName || "控件"
      // if (item.userName.length > 0) {
      //   if (item.userName.length > 2) {
      //     result = [item.userName[0], item.userName[1]].join(",") + "..."
      //   } else {
      //     result = item.userName.join(",")
      //   }
      // }
      return result
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../../assets/scss/mixins.scss";

.contract-show {
  text-align: center;
  overflow: hidden;
  /*border:1px solid green;*/
  .contract-warp {
    @include widthHeight(800px, 93vh);
    float: left;
    margin: 0px 14px 0 14px;
    overflow: auto;
    .right {
      cursor: pointer;
      display: inline-block;
      margin-left: 20px;
    }
    .countPage {
      text-align: left;
      padding: 8px;
      box-sizing: border-box;
      width: 800px;
      margin-bottom: 4px;
      border: 1px solid #f3f3f3;
    }
    .page-jump {
      width: 70px;
      margin: 0 10px 0 10px;
    }
    .pdf-warp {
      margin-top: 60px;
      overflow: hidden;
      position: relative;

      .div-drag-tempnode {
        background-color: #ffe5e5;
        opacity: 0.75;
        z-index: 1;
        position: fixed;
        pointer-events: none;
        user-select: none;

        text-align: center;
        line-height: 30px;
      }

      .movie-info {
        img {
          @include widthHeight(800px, auto);
          display: block;
        }
      }

      .height-auto {
        height: auto !important;
      }

      .vdr {
        background-color: #ffe5e5;
        opacity: 0.7;
        pointer-events: auto;
        border: 1px dashed #999;

        .control-info {
          box-sizing: border-box;
          position: absolute;
          width: 100%;
          padding: 4px;
          text-align: center;
          margin-top: -31px;
          background-color: #fff7d7;
          overflow: hidden;
          white-space: nowrap;
        }

        .btn-close {
          position: absolute;
          cursor: pointer;
          pointer-events: auto;
          top: -16px;
          right: -10px;
          font-size: 20px;
          opacity: 0.7;
        }

        .input-control {
          /*border:1px solid red;*/
          border: none;
          @include widthHeight(100%, 100%);
          font-size: 14px;
          background-color: transparent;
        }
      }
      .signRead {
        background: #fff;
        border: none;
      }
    }
  }

  .control-warp {
    box-sizing: border-box;
    width: 220px;
    height: 95vh;
    float: left;
    background-color: #fff;
    overflow: auto;
    padding: 14px;

    .selected,
    .date-set,
    .diy-set {
      .title {
        font-size: 16px;
        margin: 5px 0;
      }
    }

    .selected {
      .control-item {
        border: 1px solid #ccc;
        width: 160px;
        line-height: 30px;
        min-height: 30px;
        margin: 10px auto;
        padding: 0 5px;
        cursor: pointer;
        word-break: break-all;

        &:hover {
          background-color: #ffe5e5;
        }
      }

      .control-item--active {
        background-color: #ffe5e5;
      }
    }

    .date-set {
      text-align: left;
    }

    .diy-set {
      .label {
        margin: 10px 0;
      }

      .icon-plus-remove {
        width: 34px;
        line-height: 34px;
        text-align: center;
        font-size: 20px;
        cursor: pointer;
      }
    }
  }

  .fixed-top {
    position: fixed;
    width: 800px;
    top: 50px;
    z-index: 999;
    border: 1px solid #f3f3f3;
    background-color: #fff;
  }
  .radios {
    .el-radio {
      text-align: left;
      width: 136px;
      margin-bottom: 10px;
    }
    .el-radio:last-child {
      margin-right: 30px;
    }
  }
}
</style>
