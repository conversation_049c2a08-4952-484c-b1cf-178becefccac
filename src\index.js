import Vue from "vue"
import "./assets/scss/base.scss"
import "./assets/scss/element-variables.scss"
import "vant/lib/index.css"
import ElementUI from "element-ui"
// import "element-ui/lib/theme-chalk/index.css"
Vue.use(ElementUI)
import Vant from "vant"
import { ImagePreview } from "vant"

Vue.use(Vant)
Vue.use(ImagePreview)

import { InfiniteScroll, Spinner } from "mint-ui"
import "mint-ui/lib/style.css"
Vue.component(Spinner.name, Spinner)
Vue.use(InfiniteScroll)

import router from "./router"
import App from "./App"

//如果当前启用mock模式
// if(process.env.MOCK){
//   require('../mock/index')
// }

import filters from "filters"
Object.keys(filters).forEach(item => {
  Vue.filter(item, filters[item])
})
import "directives"

import store from "./store"
import * as AT from "@/store/actionTypes.js"

Vue.config.productionTip = false

let getParam = () => {
  let url = window.location.href
  let keyValue = ""
  let obj = {}
  if (url.indexOf("?") != -1) {
    keyValue = url.split("?")[1].split("&")
    for (let i = 0; i < keyValue.length; i++) {
      let item = keyValue[i].split("=")
      obj[item[0]] = item[1]
    }
    return obj
  }
  if (url.indexOf("#") != -1) {
    keyValue = url.split("#")[1].split("&")
    for (let i = 0; i < keyValue.length; i++) {
      let item = keyValue[i].split("=")
      obj[item[0]] = item[1]
    }
    return obj
  }
  return false
}

if (getParam()) {
  store.commit(AT.URLINFO, getParam())
  store.commit(AT.LOGIN_TOKEN, getParam().token)
}

// fetchGetConfig().then(res=> {
// window.__BASEURL__ = res.env.host;
new Vue({
  el: "#app",
  router,
  store,
  render: createElement => createElement(App)
})
// });
