<template>
  <div v-loading="loading">
    <div class="header">
      <div>签署文件</div>
      <div>
        <el-button type="primary" size="mini" @click="handleSave" v-if="contractForm.signStatus == 'ACCEPT'"
          >签署</el-button
        >
        <!-- <a class="closeBtn" href="/index.html#modules/sign/createfile.html">关闭</a> -->
      </div>
    </div>
    <div class="template-edit template-see">
      <div class="left-step-warp">
        <div class="dragItemWrap">
          <div class="step-roles">
            <div class="div-step">
              <p class="step-title">文件名称</p>
              <p>{{ contractForm.name }}</p>
            </div>
            <div class="div-step">
              <p class="step-title">文件状态</p>
              <p>{{ contractForm.signStatus | fileStatus }}</p>
            </div>
            <div v-if="contractForm.signStatus == 'ACCEPT'">
              <div class="mySign div-step" v-if="enterprise">
                <div class="btnGroup">
                  <h4>
                    企业公章
                    <!-- <el-button @click="setChapter" size="mini" type="primary">选择其他公章</el-button> -->
                  </h4>
                </div>
                <div class="moveChapter">
                  <img @click="moveChapter" class="chapter" :src="signDefaultImage" alt />
                </div>
                <p class="clickSign">签署时请点击签章</p>
              </div>
              <div class="mySign div-step" v-else>
                <div class="btnGroup">
                  <h4>
                    我的签章
                    <el-button @click="addSignName" size="mini" type="primary" v-if="enableHandSign == 'YES'"
                      >新增签名</el-button
                    >
                  </h4>
                  <div class="moveChapter" v-if="signDefaultImage">
                    <img @click="moveChapter" class="chapter" :src="signDefaultImage" alt />
                    <p class="clickTip">签署时请点击签章</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 签章选择弹窗 -->
        <el-dialog title="请选择" :visible.sync="chooseChapter" width="50%">
          <div class="dialogs">
            <el-radio
              class="signNamePicture"
              v-for="(item, index) in signList"
              :key="index"
              v-model="radio"
              :label="item"
            >
              <div class="images">
                <img :src="item" alt />
              </div>
            </el-radio>
          </div>
          <div slot="footer" class="dialog-footer" style="text-align:center;position:relative;bottom:0;height:100%;">
            <el-button @click="chooseChapter = false">取 消</el-button>
            <el-button type="primary" @click="submitChapter">确 定</el-button>
          </div>
        </el-dialog>
        <!-- 设置手写签名 -->
        <el-dialog
          title="请设置手写签名"
          :visible.sync="setSign"
          class="sign"
          width="474px"
          :close-on-click-modal="false"
        >
          <SignName :showSkip="showSkip" @saveImageInfo="saveImageInfo"></SignName>
        </el-dialog>
      </div>
      <div class="right-content-warp">
        <div class="contract-warp">
          <contract-show
            v-if="controlList && pdfPictures"
            :is-readonly="true"
            :pdfPictures="pdfPictures"
            :controlList="controlList"
            :signImage="signImage"
            :isReadOnly="true"
            ref="refContractShow"
            :isOnlySign="true"
          ></contract-show>
        </div>
      </div>
      <div class="control-warp card-box">
        <div class="seeProcess clearfix">
          <h2 class="title">流程</h2>
          <div class="audit">
            <h3>
              <span class="dot-con">
                <img src="../../assets/images/dot.png" alt />
              </span>
              签署
            </h3>
            <div v-for="(item, index) in flowStep" :key="index" class="person-content">
              <p>
                {{ item.name }}
                <span class="status" :style="{ color: item.status == 'SUCCESS' ? '#0fcf0f' : '#ff9f0f' }">{{
                  item.status | signStatus
                }}</span>
              </p>
              <p class="operat-conent">{{ item.signer }}</p>
            </div>
          </div>
        </div>
      </div>
      <div style="clear: both;"></div>
      <PopUp
        :dialogFormVisible="dialogFormVisible"
        @noreset="noreset"
        @closeBtn="closeBtn"
        @confirmSignTel="confirmSignTel"
        :cellPhone="cellPhone"
        :telVaildate="telVaildate"
        :loadBtn="loadBtn"
      ></PopUp>
    </div>
    <div
      class="mask"
      v-if="
        (contractForm.signStatus == 'IN_PROCESS' || contractForm.signStatus == 'SUCCESS') &&
          contractForm.fileStatus != 'CLOSE'
      "
    >
      <div class="tip">您已签署完成，其余签署方签署中…</div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex"
import ContractShow from "../../components/templateEdit/ContractShow"
import SignName from "../../components/basic/signName"
import PopUp from "./components/PopUp"
export default {
  components: {
    ContractShow,
    SignName,
    PopUp
  },
  computed: {
    ...mapState({
      token: "token",
      urlInfo: "urlInfo"
    })
  },
  data() {
    return {
      contactObj: {},
      dialogFormVisible: false,
      controlList: null,
      showSkip: false,
      setSign: false,
      flowStep: [],
      pdfPictures: null,
      contractForm: {},
      loading: false,
      chooseChapter: false,
      enterprise: true,
      signDefaultImage: "",
      signImage: {
        sign: "",
        seal: ""
      },
      radio: "",
      loadBtn: false,
      passed: "",
      telVaildate: false,
      refuseId: "",
      contractId: "",
      cellPhone: "",
      enableHandSign: "NO",
      enableSmsVerify: "NO",
      backUrl: "",
      signList: [],
      signImg: ""
    }
  },
  mounted() {
    this.loading = true
    this.fatchData()
  },
  methods: {
    fatchData() {
      this.$store.dispatch("templateStore/acGetSigner").then(res => {
        if (res.data.signStatus == "ACCEPT") {
          this.controlList = res.data.controls
        } else {
          this.controlList = []
        }
        this.pdfPictures = res.data.archives
        this.flowStep = res.data.steps
        this.contractForm = res.data
        if (res.data.signImage) {
          this.signDefaultImage = "data:image/png;base64," + res.data.signImage
          this.signImg = "data:image/png;base64," + res.data.signImage
        }
        this.enableHandSign = res.data.enableHandSign
        this.enableSmsVerify = res.data.enableSmsVerify
        this.cellPhone = res.data.signerMobile
        // res.data.controls.forEach(item => {
        //   if (item.type == "SIGN_CONTROL") {
        //     this.enterprise = false
        //   } else {
        //     this.enterprise = true
        //   }
        // })
        if (
          res.data.controls.some(item => {
            return item.type == "SIGN_CONTROL"
          })
        ) {
          this.enterprise = false
        }
        this.loading = false
      })
    },
    submitChapter() {
      this.signDefaultImage = this.radio
      this.chooseChapter = false
    },
    setChapter() {
      this.chooseChapter = true
    },
    addSignName() {
      this.setSign = true
    },
    moveChapter() {
      // if (this.contractForm.controls[0].type == "SIGN_CONTROL") {
      //   this.signImage.sign = this.signDefaultImage
      // } else {
      //   this.signImage.seal = this.signDefaultImage
      // }
      this.signImage.sign = this.signDefaultImage
      this.signImage.seal = this.signDefaultImage
      console.log(this.controlList)
      if (this.controlList.length > 0) {
        let controlListNew = this.controlList.filter(item => item.type == "SIGN_CONTROL" || item.type == "SEAL_CONTROL")
        let belong = controlListNew.map((item, index) => item.belong)
        console.log(controlListNew)
        this.currentPdfPage = Math.min.apply(Math, belong)
        this.$refs["refContractShow"].handleGotoPage(this.currentPdfPage)
      }
    },
    //签署
    handleSave() {
      if (this.signImage.sign || this.signImage.seal) {
        if (this.enableSmsVerify == "YES") {
          this.dialogFormVisible = true
        } else {
          let telFrom = {}
          this.resetData(telFrom)
          console.log(telFrom)
          this.$store.dispatch("templateStore/acSignFile", telFrom).then(res => {
            this.$message({
              type: "success",
              message: "已签署完成！"
            })
            if (this.urlInfo.callback) {
              window.location.href = decodeURIComponent(this.urlInfo.callback)
            } else {
              this.$router.push("/successPagePc")
            }
          })
        }
      } else {
        this.$message.error("请点击签章进行签署")
      }
    },
    // 提交数据
    resetData(telFrom) {
      console.log(this.signImage)
      if (this.enterprise) {
        telFrom.signImage = this.signImage.seal.split(",")[1]
      } else {
        telFrom.signImage = this.signImage.sign.split(",")[1]
      }
    },
    //手机号验证
    confirmSignTel(telFrom) {
      this.resetData(telFrom)
      this.loadBtn = true
      this.$store
        .dispatch("templateStore/acSignFile", telFrom)
        .then(res => {
          this.$message({
            type: "success",
            message: "已签署成功！"
          })
          this.dialogFormVisible = false
          this.fatchData()
          if (this.urlInfo.callback) {
            window.location.href = decodeURIComponent(this.urlInfo.callback)
          } else {
            this.$router.push("/successPagePc")
          }
        })
        .catch(error => {
          this.loadBtn = false
        })
    },
    closeBtn() {
      this.dialogFormVisible = false
      this.loadBtn = false
    },
    noreset() {
      this.dialogFormVisible = false
    },
    saveImageInfo(imageSrc) {
      this.setSign = false
      if (this.signDefaultImage) {
        this.signList = []
        this.chooseChapter = true
        this.signList.push(this.signImg)
        this.signList.push(imageSrc)
      } else {
        this.signDefaultImage = imageSrc
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import "../../assets/scss/mixins.scss";
.template-see {
  .left-step-warp {
    background-color: #fff;
    height: 95vh;
    width: 220px;
    float: left;
    overflow-x: hidden;

    .step-roles {
      .div-step {
        border: 1px solid #e6e6e6;
        margin: 10px;
        padding: 10px;
        p {
          color: #666666;
        }
        .step-title {
          color: #252525;
          margin-bottom: 15px;
        }
      }
    }

    .moveChapter {
      img {
        display: block;
        width: 180px;
        margin: 0 auto;
      }
      .tip {
        text-align: center;
        line-height: 20px;
        font-size: 14px;
        color: #666666;
        padding: 10px 0;
      }
      .clickTip {
        text-align: center;
        line-height: 35px;
        color: #666666;
      }
    }
    .clickSign {
      text-align: center;
      line-height: 35px;
      color: #666666;
    }
    .btnGroup {
      margin-top: 10px;
      h4 {
        overflow: hidden;
        height: 28px;
        line-height: 28px;
        margin-bottom: 20px;
        font-size: 14px;
        .el-button {
          float: right;
        }
      }
    }

    .el-dialog {
      height: auto;
      .dialogs {
        overflow-y: auto;
        position: relative;
      }
      .twoCode {
        margin: 0 auto;
        width: 206px;
        img {
          display: block;
          width: 100%;
        }
      }
      .twoText {
        margin: 10px auto;
        padding: 10px 30px;
        h4 {
          font-size: 16px;
          padding: 10px 0;
          font-weight: bold;
        }
        p {
          color: #333;
          font-size: 14px;
          line-height: 25px;
        }
      }
    }

    .signNamePicture {
      .images {
        width: 200px;
        height: 200px;
        border: 1px solid #ddd;
        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .right-content-warp {
    width: 824px;
    height: 100vh;
    float: left;
  }

  .right-content-warp {
    width: 824px;
    height: 100vh;
    float: left;
  }
  .card-box {
    box-sizing: border-box;
    width: 220px;
    height: 95vh;
    float: left;
    background-color: #fff;
    padding: 0 14px;
    overflow: auto;
    .gray {
      color: #ccc;
    }
    .seeProcess {
      .title {
        font-size: 16px;
        color: #252525;
        line-height: 40px;
        border-bottom: 1px solid #ededed;
      }
    }
    .audit {
      margin-bottom: 30px;
      font-size: 14px;
      color: #333333;
      h3 {
        font-size: 16px;
        line-height: 40px;
        color: #252525;
        font-weight: bold;
      }
      p {
        line-height: 25px;
      }
      .status {
        color: #666666;
        float: right;
        font-size: 14px;
      }
      .person-content {
        border-bottom: 1px solid #ededed;
        padding: 10px 0;
      }
    }
  }
}
.mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  .tip {
    width: 400px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    margin: 20% auto;
    text-align: center;
  }
}
</style>
