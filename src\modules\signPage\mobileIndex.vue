<template>
  <div class="mobile-warp" :class="isShowChooseSeal ? mobileHeight : ''">
    <div class="my-signBox">
      <div class="my-sign" @click="mySign">回到签署位置</div>
    </div>
    <!-- <div class="handSeal-warp" v-if="isShowChooseSeal">
     
    </div> -->

    <chooseSeal
      :imageFile="imageFile"
      :signImg="signImg"
      @useSeal="useSeal"
      :enterprise="enterprise"
      :enableHandSign="enableHandSign"
      @returnButton="returnButton"
      ref="ref-seal"
    ></chooseSeal>

    <div class="up-layer">
      <span class="dropdown-link" @click="handleDropdown">
        查看签署详情
        <i :class="showDropdown ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"></i>
      </span>
      <div class="dropdown" v-show="showDropdown">
        <p class="file-title">{{ info.name }}</p>
        <p class="file-status">
          签署状态：<span class="text-status" :class="info.signStatus == 'ACCEPT' ? 'blue' : 'green'">{{
            info.signStatus | fileStatus
          }}</span>
        </p>
        <div class="audit">
          <div class="steps_item" v-for="(item, i) in flowStep" :key="i">
            <div v-if="flowStep.length > 1" class="steps_point" :class="{ current: i == flowStep.length - 1 }"></div>
            <div v-else class="steps_line"></div>
            <p class="top">
              {{ item.name }}
              <span class="status" :style="{ color: item.status == 'SUCCESS' ? '#0fcf0f' : '#ff9f0f' }">
                {{ item.status | signStatus }}
              </span>
            </p>
            <p class="operat-conent">{{ item.signer }}</p>
            <div class="line" v-show="flowStep.length > 1 && i < flowStep.length - 1"></div>
          </div>
        </div>
      </div>
    </div>
    <div style="position: fixed;top:10px;right:10px;z-index: 1;color:#606266;">{{ currentPage }}/{{ total }}</div>
    <div class="pdf-content">
      <div class="img-warp" :id="'img' + (index + 1)" v-for="(item, index) in pdfList" :key="index">
        <img class="img-pdf" :src="item" :alt="index" style="width:100%;height:100%;" @click="getImg(item, index)" />
        <div v-if="info.signStatus == 'ACCEPT'">
          <div>
            <span v-if="signImg">
              <span v-for="(it, index) in signList[index + 1]" :key="index">
                <span v-if="it.type === 'DATE_CONTROL'">
                  <div class="signDate" :style="{ left: it.x + '%', top: it.y + '%', width: it.w + '%' }">
                    签署日期
                  </div>
                </span>
                <span v-if="it.type === 'SIGN_CONTROL' || it.type === 'SEAL_CONTROL'">
                  <img
                    class="signImg"
                    :src="signImg"
                    :style="{ left: it.x + '%', top: it.y + '%', width: it.w + '%' }"
                    @click="showChooseSeal"
                  />
                </span>
              </span>
            </span>
            <span v-else>
              <span v-for="(it, index) in signList[index + 1]" :key="index">
                <span v-if="it.type === 'DATE_CONTROL'">
                  <div class="signDate" :style="{ left: it.x + '%', top: it.y + '%', width: it.w + '%' }">
                    签署日期
                  </div>
                </span>
                <span v-if="it.type === 'SIGN_CONTROL' || it.type === 'SEAL_CONTROL'">
                  <!-- <img
                    class="signImg"
                    src="../../assets/images/default.png"
                    :style="{ left: it.x + '%', top: it.y + '%', width: it.w + '%' }"
                    @click="isShowChooseSeal = true"
                  /> -->
                  <span class="signImg" :style="{ left: it.x + '%', top: it.y + '%' }" @click="showChooseSeal">
                    <img src="../../assets/images/signature.png" alt="" />
                    <p>个人签章</p>
                  </span>
                </span>
              </span>

              <!-- <img
                v-for="(it, index) in signList[index + 1]"
                :key="index"
                class="signImg"
                src="../../assets/images/default.png"
                :style="{ left: it.x + '%', top: it.y + '%', width: it.w + '%' }"
                @click="isShowChooseSeal = true"
              /> -->
            </span>
          </div>
        </div>
      </div>
      <div v-show="noMore" class="v-infinite-scroll-noMore">没有更多了</div>
    </div>
    <div class="footer" v-if="info.signStatus == 'ACCEPT'">
      <div class="left" @click="showChooseSeal">
        <img src="../../assets/images/signature.png" alt="" />
        <p>个人签章</p>
      </div>
      <el-button class="right" :type="type" @click="showSignPopup" :disabled="!isShowSealList" :loading="loading"
        >提交签署</el-button
      >
    </div>
    <PopUp
      :dialogFormVisible="dialogFormVisible"
      @noreset="noreset"
      @closeBtn="closeBtn"
      @confirmSignTel="confirmSignTel"
      :cellPhone="info.signerMobile"
      :telVaildate="telVaildate"
      :loadBtn="loadBtn"
      width="80%"
    ></PopUp>
    <!-- 签署成功遮罩 -->
    <div
      class="mask"
      v-if="(info.signStatus == 'IN_PROCESS' || info.signStatus == 'SUCCESS') && info.fileStatus != 'CLOSE'"
    >
      <div class="tip">您已签署完成，其余签署方签署中…</div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex"
import chooseSeal from "../../components/mobile/chooseSeal"
import handwriting from "../../components/mobile/handwrite"
import { error } from "util"
import { ImagePreview } from "vant"
import PopUp from "../contract/components/PopUp"

const TIME_COUNT = 60

export default {
  components: {
    chooseSeal,
    PopUp,
    handwriting
  },
  data() {
    return {
      show: true,
      currentPage: 1,
      imgScale: 1,
      isShowChooseSeal: false,
      imageFile: "",
      isShowSealList: false,
      info: {},
      telVaildate: false,
      pdfList: [],
      flowStep: [],
      busy: true,
      loadBtn: false,
      loadingShow: false,
      noMore: false,
      enableHandSign: "",
      enableSmsVerify: "",
      ruleForm: {
        page: 0
      },
      total: null,
      tempHeight: "",
      signList: [],
      enterprise: true,
      dialogFormVisible: false,
      sms: "",
      count: "",
      showTime: true,
      disabled: false,
      codeForm: {
        code: "",
        mobile: "",
        imageBase64: ""
      },
      mobileHeight: "mobileHeight",
      signImg: "",
      src: require("../../assets/images/default.png"),
      rules: {
        code: [{ required: true, message: "请输入验证码", trigger: "blur" }]
      },
      type: "primary",
      loading: false,
      loadComfirm: false,
      telFrom: {
        phoneCode: "",
        smsToken: ""
      },
      showDropdown: false
    }
  },
  computed: {
    ...mapState({
      urlInfo: "urlInfo"
    })
  },
  mounted() {
    // history.pushState(null, null, document.URL)
    // window.addEventListener("popstate", function() {
    //   history.pushState(null, null, document.URL)
    // })
    window.addEventListener("scroll", this.handlePageScroll)
    //获取合同头信息
    this.$store.dispatch("templateStore/acGetSigner").then(res => {
      console.log(res)
      this.info = res.data
      this.pdfList = res.data.archives
      this.total = this.pdfList.length
      this.flowStep = res.data.steps
      this.imageFile = res.data.signImage
      this.enableHandSign = res.data.enableHandSign
      this.enableSmsVerify = res.data.enableSmsVerify
      let signInfo = res.data.controls
      this.handleSelectSeal(signInfo)
      // res.data.controls.forEach(item => {
      //   if (item.type == "SIGN_CONTROL") {
      //     this.enterprise = false
      //   } else {
      //     this.enterprise = true
      //   }
      // })
      if (
        res.data.controls.some(item => {
          return item.type == "SIGN_CONTROL"
        })
      ) {
        this.enterprise = false
      }
    })
  },
  methods: {
    handleDropdown() {
      this.showDropdown = !this.showDropdown
    },

    showChooseSeal() {
      this.$refs["ref-seal"].show = true
    },

    returnButton() {
      this.$refs["ref-seal"].show = false
      this.isShowChooseSeal = false
    },
    getImg(images, index) {
      const myImg = []
      if (this.pdfList) {
        this.pdfList.forEach(item => {
          myImg.push(item)
        })
      }
      ImagePreview({
        images: myImg,
        showIndex: true,
        loop: false,
        startPosition: index
      })
    },
    useSeal(val) {
      console.log(val)
      this.$refs["ref-seal"].show = false
      this.isShowChooseSeal = false
      this.signImg = val
      this.isShowSealList = true
      this.type = "primary"
      this.mySign()
    },
    handlePageScroll() {
      this.tempHeight = document.getElementsByClassName("img-warp")[0].clientHeight - 0.3
      this.currentPage = Math.round(window.scrollY / this.tempHeight) + 1
    },
    scrollSmoothTo(position) {
      if (!window.requestAnimationFrame) {
        window.requestAnimationFrame = function(callback, element) {
          return setTimeout(callback, 17)
        }
      }
      // 当前滚动高度
      var scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      // 滚动step方法
      var step = function() {
        // 距离目标滚动距离
        var distance = position - scrollTop
        // 目标滚动位置
        scrollTop = scrollTop + distance / 5
        if (Math.abs(distance) < 1) {
          window.scrollTo(0, position)
        } else {
          window.scrollTo(0, scrollTop)
          requestAnimationFrame(step)
        }
      }
      step()
    },
    handleScroll(imgNum) {
      let tempOffsetTop = document.getElementById("img" + imgNum).offsetTop
      if (!window.requestAnimationFrame) {
        window.scrollTo({ top: tempOffsetTop })
      } else {
        this.scrollSmoothTo(tempOffsetTop)
      }
    },
    //选择印章
    handleSelectSeal(signInfo, widthInfo) {
      //seal印章   sign合同
      let obj = {}
      signInfo.forEach(item => {
        let myWidth = document.body.clientWidth
        let mycoe = 1190 / 1683
        let myHiehgt = myWidth / mycoe
        item.x = ((item.xAxis * myWidth) / myWidth) * 100 //x坐标
        item.y = ((item.yAxis * myHiehgt) / myHiehgt) * 100 //y坐标
        item.w = ((item.width * myWidth) / myWidth) * 100 //y坐标
        if (obj[item.belong]) {
          obj[item.belong].push(item)
        } else {
          obj[item.belong] = [item]
        }
      })
      this.signList = obj
      console.log(this.signList)
    },
    //验证码倒计时
    send() {
      if (!this.timer) {
        this.count = TIME_COUNT
        this.showTime = false
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
          } else {
            this.showTime = true
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }
    },
    getVerifyCode() {
      //获取短信验证码
      if (this.cellPhone != "") {
        this.$store.dispatch("templateStore/acSend").then(res => {
          this.telFrom.smsToken = res.data
          this.send()
        })
      }
    },
    showSignPopup() {
      this.loading = true
      if (this.enableSmsVerify == "YES" && this.signImg) {
        this.dialogFormVisible = true
        this.loading = false
      } else if (this.enableSmsVerify == "NO" && this.signImg) {
        this.codeForm.imageBase64 = this.signImg.substring(this.signImg.indexOf(",") + 1)
        this.$store.dispatch("templateStore/acSignFile", { signImage: this.codeForm.imageBase64 }).then(res => {
          this.$toast.success("已签署完成！")
          if (this.urlInfo.callback) {
            window.location.href = decodeURIComponent(this.urlInfo.callback)
          } else {
            this.$router.push("/successPageH5")
          }
        })
      }
    },
    // 提交数据
    resetData(telFrom) {
      telFrom.signImage = this.signImg.split(",")[1]
    },
    //手机号验证
    confirmSignTel(telFrom) {
      this.resetData(telFrom)
      console.log(telFrom)
      this.loadBtn = true
      this.$store
        .dispatch("templateStore/acSignFile", telFrom)
        .then(res => {
          this.$toast.success("已签署完成！")
          this.dialogFormVisible = false
          if (this.urlInfo.callback) {
            window.location.href = decodeURIComponent(this.urlInfo.callback)
          } else {
            this.$router.push("/successPageH5")
          }
        })
        .catch(error => {
          this.loadBtn = false
        })
    },
    closeBtn() {
      this.dialogFormVisible = false
      this.loadBtn = false
    },
    noreset() {
      this.dialogFormVisible = false
    },
    // 点击我的签章定位到指定位置
    mySign() {
      console.log(this.info, "this.info")
      this.currentPage = this.info.controls[0].belong || 1
      this.tempHeight = document.getElementsByClassName("img-warp")[0].clientHeight - 0.3
      this.scrollSmoothTo((this.currentPage - 1) * this.tempHeight)
    }
  }
}
</script>
<style lang="scss" scoped>
.mobileHeight {
  height: 100%;
  overflow: hidden;
}
.mobile-warp {
  height: 100%;
  .my-signBox {
    position: fixed;
    top: 50px;
    right: -12px;
    z-index: 1;
    width: 94px;
    height: 30px;
    border: 1px solid #4f71ff;
    border-radius: 22px;
    line-height: 30px;
    text-align: center;
    padding-right: 10px;
    overflow: hidden;
    background-color: #fff;
    .my-sign {
      color: #4f71ff;
      font-size: 12px;
    }
  }
  .handSeal-warp {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
    z-index: 9;
  }
  .up-layer {
    position: fixed;
    top: 0;
    left: 0;
    padding-left: 10px;
    z-index: 1;
    width: 100%;
    height: 37px;
    line-height: 37px;
    background: #fff;
    .dropdown-link {
      font-size: 13px;
      color: #53595c;
    }

    .name,
    .endTime,
    .pageNumber {
      background: rgba(19, 12, 13, 0.1);
    }

    .endTime {
      margin-top: 10px;
    }

    .dropdown {
      margin-top: 10px;
      width: 95%;
      background: #fff;
      box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.16);
      line-height: 16px;
      padding: 20px 16px;
      border-radius: 6px;
      box-sizing: border-box;
      position: relative;
      &:before {
        content: "";
        border: 10px solid transparent;
        border-bottom-color: #e8e8e8;
        position: absolute;
        left: 20px;
        top: 0;
        margin-top: -20px;
      }
      &:after {
        content: "";
        border: 10px solid transparent;
        border-bottom-color: #fff;
        position: absolute;
        top: 0;
        left: 20px;
        margin-top: -19px;
      }

      .file-title {
        font-size: 16px;
        color: #46485a;
        margin-bottom: 20px;
      }
      .file-status {
        font-size: 16px;
        color: #777c94;
      }
    }
  }
  .file-name {
    font-size: 16px;
    color: #46485a;
  }
  .file-status {
    font-size: 16px;
    color: #777c94;
  }

  .page-infinite-loading {
    width: 40px;
    margin: 0 auto;
  }
  .pdf-content {
    padding-top: 37px;
    margin-bottom: 45px;
  }
  .img-warp {
    position: relative;
    width: 100%;
    min-height: auto;
    margin: 0 auto;
    background-size: 100%;
    // .signImg {
    //   position: absolute;
    //   height: auto;
    // }
    .signImg {
      position: absolute;
      height: auto;
      width: 77px;
      height: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: rgba(79, 113, 255, 0.06);
      border: 1px dashed #4f71ff;
      border-radius: 1px;
      box-sizing: border-box;
      img {
        width: 16px;
        height: 16px;
      }
      p {
        color: #24262a;
        font-size: 12px;
      }
    }

    .signDate {
      position: absolute;
      height: 20px;
      width: 100px;
    }
    .control-warp {
      width: 150px;
      height: 70px;
      line-height: 70px;
      background-color: rgba(0, 0, 0, 0.6);
      color: #ffffff;
      text-align: center;
      position: absolute;
      left: 50px;
      top: 150px;
    }
  }
  .v-infinite-scroll-noMore {
    text-align: center;
    padding: 10px 0;
    font-size: 12px;
    color: #aaa;
  }
  .pageNumber {
    text-align: center;
    position: absolute;
    bottom: 30px;
    left: 50%;
    z-index: 666;
    transform: translateX(-14px);
  }

  .footer {
    background: #fff;
    position: fixed;
    bottom: 0px;
    height: 88px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px 24px;
    box-sizing: border-box;
    box-shadow: 0 -1px 8px 0 rgba(224, 224, 224, 0.6);
    .left {
      width: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
      }
      p {
        color: #24262a;
        font-size: 12px;
      }
    }
    .right {
      height: 46px;
      width: 50%;
      font-size: 16px;
      font-weight: 400;
    }
    // text-align: center;
    // line-height: 50px;
    cursor: pointer;
    // button {
    //   width: 100%;
    //   height: 45px;
    //   border-radius: 0;
    // }
    .el-button--primary:hover,
    .el-button--primary:focus {
      border: none;
    }
    .el-button--primary {
      border: none;
    }
  }

  .bigger,
  .smaller {
    position: fixed;
    right: 10px;
    font-size: 30px;
    width: 30px;
    height: 30px;
    line-height: 26px;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.2);
    text-align: center;
  }

  .bigger {
    bottom: 340px;
  }

  .smaller {
    bottom: 260px;
  }
  .tip {
    font-size: 12px;
    margin: 10px 0;
    color: #7d7e80;
  }
  .num {
    font-size: 16px;
    margin-bottom: 10px;
    color: #7d7e80;
  }
}
.seeProcess {
  .title {
    font-size: 14px;
    color: #888;
    border-bottom: 1px solid #ededed;
  }
}

.audit {
  margin-top: 20px;
  font-size: 14px;
  color: #333333;

  .top {
    font-size: 14px;
    color: #888;
    margin-bottom: 16px;
  }
  .bottom {
    font-size: 16px;
    color: #333333;
  }
  .status {
    color: #666666;
    float: right;
    font-size: 14px;
  }
  .steps_item {
    width: 100%;
    position: relative;
    margin-left: 14px;
    margin-bottom: 20px;
    box-sizing: border-box;
    padding: 0 12px;
    .steps_point {
      width: 12px;
      height: 12px;
      border: 2px solid #4f71ff;
      position: absolute;
      top: 0;
      left: -15px;
      border-radius: 50%;
      z-index: 9;
    }
    .steps_line {
      width: 4px;
      height: 12px;
      border-radius: 1px;
      background: #4f71ff;
      position: absolute;
      top: 2px;
      left: -10px;
    }
    .current {
      left: -15px;
      width: 12px;
      height: 12px;
      background: #fff;
      border: 2px solid #ccc;
      z-index: 9;
    }
    .line {
      width: 1px;
      height: 37px;
      border-left: 1px solid #4f71ff;
      position: absolute;
      left: -8px;
      top: 25px;
    }
  }
}
.mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  .tip {
    width: 80%;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    margin: 40% auto;
    text-align: center;
  }
}

.text-status {
  display: inline-block;
  width: 52px;
  height: 20px;
  border-radius: 3px;
  font-size: 12px;
  text-align: center;
  line-height: 20px;
}
.blue {
  color: #4f71ff;
  background: #f5f7ff;
}
.green {
  color: #41bd5a;
  background: #f0faf0;
}
</style>
