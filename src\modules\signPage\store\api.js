// import { fetch } from 'request/fetch'
// //签署页面信息展示
// export function getInfo() {
//   return fetch({
//     url: '/openapi/v1.0/web/task/get/info',
//     method: 'post',
//     data: {
//       body: {
//         // taskNo: ruleForm.taskNo,
//         // batchNo: ruleForm.batchNo,
//         sessionKey: window.__BASE_ENV__.token
//       }
//     }
//   })
// }

// //签署文件预览
// export function review(ruleForm) {
//   return fetch({
//     url: '/openapi/v1.0/web/task/review/page',
//     method: 'post',
//     data: {
//       body: {
//         // taskNo: ruleForm.taskNo,
//         // batchNo: ruleForm.batchNo,
//         page: ruleForm.page,
//         sessionKey: window.__BASE_ENV__.token
//       }
//     }
//   })
// }

// //签署印章信息
// export function sealList() {
//   return fetch({
//     url: '/openapi/v1.0/web/task/seal/list',
//     method: 'post',
//     data: {
//       body: {
//         // taskNo: ruleForm.taskNo,
//         // batchNo: ruleForm.batchNo,
//         sessionKey: window.__BASE_ENV__.token
//       }
//     }
//   })
// }

// //确认签署
// export function taskSign(ruleForm) {
//   return fetch({
//     url: '/openapi/v1.0/web/page/sign',
//     method: 'post',
//     data: {
//       body: {
//         mobile: ruleForm.mobile,
//         code: ruleForm.code,
//         sessionKey: window.__BASE_ENV__.token,
//         imageBase64:ruleForm.imageBase64
//       }
//     }
//   })
// }

// // 发送验证码
// export function apiSend(ruleForm) {
//   //生成随机数
//   function guid() {
//     return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
//         var r = Math.random() * 16 | 0,
//             v = c == 'x' ? r : (r & 0x3 | 0x8);
//         return v.toString(16);
//     });
//   }
//   return fetch({
//     url: '/openapi/v1.0/web/sms/send',
//     method: 'post',
//     data: {
//       body: {
//         mobile: ruleForm.mobile,
//         userId: ruleForm.userId,
//         fileId: ruleForm.fileId,
//         requestNo:guid()
//       }
//     }
//   })
// }
