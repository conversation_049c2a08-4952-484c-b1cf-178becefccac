import { apiGetTemplate, apiSaveTemplate, apiGetSigner, apiSignFile, apiSend } from "./api"

import * as AT from "./actionTypes"

//获取模板信息
export const acGetTemplate = ({ commit }, data) => {
  return apiGetTemplate(data).then(res => {
    return res
  })
}

//保存模板信息
export const acSaveTemplate = ({ commit }, data) => {
  return apiSaveTemplate(data).then(res => {
    return res
  })
}

//查看文件
export const acGetSigner = ({ commit }, data) => {
  return apiGetSigner(data).then(res => {
    return res
  })
}

//签署文件
export const acSignFile = ({ commit }, data) => {
  return apiSignFile(data).then(res => {
    return res
  })
}

//发送短信
export const acSend = ({ commit }, data) => {
  return apiSend(data).then(res => {
    return res
  })
}
