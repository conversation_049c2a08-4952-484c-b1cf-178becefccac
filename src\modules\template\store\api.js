import fetch from "request/fetch"

//查询模板
export function apiGetTemplate(data) {
  return fetch({
    url: "/api/signing/template/query",
    method: "post", //请求方法
    data
  })
}

//编辑模板
export function apiSaveTemplate(data) {
  return fetch({
    url: "/api/signing/template/perfect",
    method: "post", //请求方法
    data
  })
}

//查看文件
export function apiGetSigner(data) {
  return fetch({
    url: "/api/signing/file/query",
    method: "post", //请求方法
    data
  })
}

//签署文件
export function apiSignFile(data) {
  return fetch({
    url: "/api/signing/file/sign",
    method: "post", //请求方法
    data
  })
}

//发送短信
export function apiSend(data) {
  return fetch({
    url: "/api/signing/sms/send",
    method: "post", //请求方法
    data
  })
}
