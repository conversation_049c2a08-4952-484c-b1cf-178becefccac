<template>
  <div v-loading="loading">
    <div class="header">
      <div>{{ name }}</div>
      <div>
        <el-button type="primary" size="mini" @click="handleSave">提交</el-button>
        <!-- <el-button type="text" class="closeBtn" size="mini" @click="handleClosed">关闭</el-button> -->
      </div>
    </div>
    <div class="template-edit">
      <div class="left-step-warp" style="user-select:none;">
        <el-scrollbar style="height:100%" class="no-xScroll">
          <div class="dragItemWrap">
            <div class="div-step step-roles">
              <div class="step-title"><span class="sp-step-num"></span>签署步骤：</div>
              <div v-for="(item, index) in contractFormData.flowStep" :key="index" class="div-role-item">
                <div @mousedown="handleSelectControl(item)">
                  <p class="pinfo">
                    <span>{{ item.name }}</span>
                  </p>
                  <p class="pstep ellipsis">{{ item.signType == "PERSONAL" ? "个人签名" : "企业印章" }}</p>
                </div>
              </div>
            </div>
            <div class="div-step step-position">
              <div class="step-title"><span class="sp-step-num"></span>待填充字段列表</div>
              <div v-for="(item, index) in fields" :key="index">
                <div class="div-role-item" @mousedown="handleSelectControl(item)">
                  <i class="el-icon-edit"></i>
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="right-content-warp">
        <div class="contract-warp">
          <contract-show
            ref="refContractShow"
            :controlList="controlListBack"
            :contactObj="contactObj"
            :isReadonly="false"
            :pdfPictures="pdfPictures"
          ></contract-show>
        </div>
      </div>
      <div style="clear: both;"></div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex"
import ContractShow from "../../components/templateEdit/ContractShow"

export default {
  components: {
    ContractShow
  },
  props: {},
  computed: {
    ...mapState({
      token: "token",
      urlInfo: "urlInfo"
    })
  },
  data() {
    return {
      contactObj: {
        currentSelectRole: {},
        controlType: "",
        presetControlInfo: {},
        dragTempnodeShow: false
      },
      name: "",
      controlListBack: [],
      fields: null,
      pdfPictures: [],
      loading: false,
      contractFormData: {
        controlList: [],
        flowStep: []
      },
      flag: false,
      tempControlList: []
    }
  },
  mounted() {
    this.loading = true
    this.fatchData()
  },
  methods: {
    fatchData() {
      this.$store.dispatch("templateStore/acGetTemplate").then(res => {
        this.pdfPictures = res.data.archives
        this.contractFormData.flowStep = res.data.steps
        this.fields = res.data.fields
        this.name = res.data.name
        this.loading = false
      })
    },
    // handleClosed() {
    //   this.$router.go(0)
    // },
    handleSelectControl(item) {
      const t = this
      let fieldInfo = null
      if (item.signType == "PERSONAL") {
        t.contactObj.controlType = "SIGN_CONTROL"
      } else if (item.signType == "ENTERPRISE") {
        t.contactObj.controlType = "SEAL_CONTROL"
      } else {
        t.contactObj.controlType = "FIELD_CONTROL"
        // fieldInfo = item
      }
      t.contactObj.dragTempnodeShow = true
      t.contactObj.currentSelectRole = item
      if (fieldInfo) {
        t.contactObj.presetControlInfo = fieldInfo
      } else {
        t.contactObj.presetControlInfo = null
      }
    },
    handleNext() {
      window.location.href = `${window.location.host}/index.html#modules/sign/signfilemaster.html`
    },
    // 验证哪些人没有拖拽控件
    checkControl() {
      this.tempControlList = this.$refs["refContractShow"].setControlList(0)
      let newTempControlList = []
      this.tempControlList.forEach(item => {
        newTempControlList.push(item.name)
      })
      let newControList = []
      if (this.fields) {
        newControList = [...this.contractFormData.flowStep, ...this.fields]
      } else {
        newControList = this.contractFormData.flowStep
      }
      this.flag = newControList.every(item => {
        return newTempControlList.includes(item.name)
      })
    },
    handleSave() {
      const t = this
      t.checkControl()
      if (this.flag) {
        const params = {
          controls: this.tempControlList
        }
        t.$store.dispatch("templateStore/acSaveTemplate", params).then(res => {
          t.$message({
            message: "模板已编辑完成！",
            type: "success"
          })
          if (this.urlInfo.callback) {
            window.location.href = decodeURIComponent(this.urlInfo.callback)
          } else {
            this.$router.push("/successPagePc")
          }
        })
      } else {
        this.$message.error("请确保所有签署步骤及待填充字段都在模板中确定位置后，再发起提交")
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../assets/scss/mixins.scss";
.left-step-warp {
  background-color: #fff;
  height: 95vh;
  width: 220px;
  float: left;
  overflow-y: auto;

  .div-step {
    .step-title {
      box-sizing: border-box;
      height: 50px;
      line-height: 50px;
      background: #f2f7ff;
      padding-left: 15px;

      .sp-step-num {
        color: #4687f5;
      }
    }
    .div-role-item {
      cursor: pointer;
      // height: 50px;
      line-height: 25px;
      padding: 10px 5px;
      border-bottom: 1px solid #f3f3f3;
      font-size: 14px;
      box-sizing: border-box;

      .pinfo {
        line-height: 24px;
        position: relative;
        font-size: 14px;

        .tipToB {
          font-size: 12px;
          background: #00bec0;
          color: #fff;
          display: inline-block;
          height: 25px;
          width: 25px;
          text-align: center;
          border-radius: 25px;
          margin-left: 10px;
        }
      }

      .pstep {
        font-size: 12px;
        color: #999;
        line-height: 26px;
        width: 80%;
      }

      &:hover {
        background-color: #f1f1f1;
      }
    }
  }
}

.right-content-warp {
  width: 1050px;
  height: 95vh;
  float: left;
}
</style>
