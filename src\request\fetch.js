//初始化 old-fetch
import { createFetch } from "old-fetch"
import { Message } from "element-ui"
import { Toast } from "vant"
import router from "../router"
const options = {
  isMock: false,
  needStartLimit: true,
  handleResponseError: (err, config) => {
    // console.log(err)
    if (err.errorCode == "2") {
      router.replace({
        path: "/loseEfficacy"
      })
    } else if (err.message) {
      if (window.outerWidth > 1024) {
        Message.error(err.message)
      } else {
        Toast.fail(err.message)
      }
    }
    if (err.toString().includes("timeout")) {
      return Promise.reject(err)
    }
  }
}
const fetch = createFetch(
  {
    // baseURL: '/api/psalary/',
  },
  options
)
export default fetch
