import Vue from "vue"
import Router from "vue-router"
import { routerConfig } from "./routerConfig"

//router
import home from "modules/home"
import templateRouter from "modules/template/router"
import contractRouter from "modules/contract/router"
import signPageRouter from "modules/signPage/router"

Vue.use(Router)
var base = "/"
if (window.location.href.includes("/hrsaas")) {
  const tt = window.location.href.split("/")
  const index = tt.findIndex(item => item === "hrsaas")
  base = "/" + tt.slice(index - 1, index + 2).join("/")
}
console.log("vue router base", base)
let router = new Router({
  base: base,
  mode: "history",
  saveScrollPosition: true,
  scrollBehavior: () => ({
    y: 0
  }),
  routes: [
    {
      path: "/home",
      name: "home",
      component: home
    },
    ...templateRouter,
    ...contractRouter,
    ...signPageRouter
  ]
})
routerConfig(router)
export default router
