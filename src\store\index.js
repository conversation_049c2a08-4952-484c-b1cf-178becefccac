import Vue from "vue"
import Vuex from "vuex"
import createPersistedState from "vuex-persistedstate"

//各模块store
import { contractStore } from "modules/contract/store"
import { templateStore } from "modules/template/store"
Vue.use(Vuex)

import mutations from "./mutations"
import actions from "./action"

export default new Vuex.Store({
  modules: {
    contractStore,
    templateStore
  },
  state: {
    urlInfo: {},
    token: ""
  },
  mutations,
  actions,
  plugins: [
    createPersistedState({
      storage: sessionStorage
    })
  ],
  urlInfo: null
})
